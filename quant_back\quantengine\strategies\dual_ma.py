import pandas as pd
import numpy as np
import vectorbt as vbt
import logging
from typing import Dict, Any, Optional
from .base_strategy import BaseStrategy

class DualMaStrategy(BaseStrategy):
    """双均线策略，结合短期和长期均线"""

    def __init__(self, params: Optional[Dict[str, Any]] = None):
        super().__init__(params)
        self.fast_window = self.params.get('fast_window', 5)
        self.mid_window = self.params.get('mid_window', 20)
        self.slow_window = self.params.get('slow_window', 60)
        self.logger = logging.getLogger('quantengine.strategy')

    @staticmethod
    def get_params_schema():
        return {
            "fast_window": {
                "type": "integer",
                "title": "短期均线周期",
                "default": 5,
                "minimum": 2,
                "maximum": 20
            },
            "mid_window": {
                "type": "integer",
                "title": "中期均线周期",
                "default": 20,
                "minimum": 10,
                "maximum": 50
            },
            "slow_window": {
                "type": "integer",
                "title": "长期均线周期",
                "default": 60,
                "minimum": 30,
                "maximum": 200
            }
        }

    def run(self, data):
        """
        运行双均线策略

        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame

        返回:
            tuple: (entries, exits) 买入和卖出信号
        """
        # 检查数据是否足够
        if len(data) < self.slow_window:
            self.logger.warning(f"数据长度({len(data)})小于慢速均线周期({self.slow_window})，可能导致信号不准确")

        # 计算三条移动平均线
        fast_ma = vbt.MA.run(data['close'], window=self.fast_window)
        mid_ma = vbt.MA.run(data['close'], window=self.mid_window)
        slow_ma = vbt.MA.run(data['close'], window=self.slow_window)

        # 生成信号：短期均线上穿中期均线
        entries = fast_ma.ma_above(mid_ma)

        # 生成信号：短期均线下穿中期均线
        exits = fast_ma.ma_below(mid_ma)

        # 确保信号是布尔类型
        entries = entries.astype(bool)
        exits = exits.astype(bool)

        # 处理NaN值（前window-1个值通常是NaN）
        entries = entries.fillna(False)
        exits = exits.fillna(False)

        # 添加日志输出，检查生成的信号数量
        self.logger.info(f"Generated {entries.sum()} entry signals and {exits.sum()} exit signals")

        # 如果有信号，打印前几个信号的日期，用于调试
        if entries.sum() > 0:
            entry_dates = data.index[entries].tolist()[:5]
            self.logger.debug(f"前5个入场信号日期: {entry_dates}")
        if exits.sum() > 0:
            exit_dates = data.index[exits].tolist()[:5]
            self.logger.debug(f"前5个出场信号日期: {exit_dates}")

        return entries, exits