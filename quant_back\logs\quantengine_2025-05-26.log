2025-05-26 08:59:07 - quantengine - INFO - 使用数据源: akshare
2025-05-26 08:59:07 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 08:59:07 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 08:59:07 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 08:59:07 - quantengine - INFO - 开始回测: 600000
2025-05-26 08:59:07 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 08:59:07 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 08:59:07 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 08:59:07 - quantengine - INFO - 开始执行策略计算...
2025-05-26 08:59:08 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 08:59:08 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 08:59:08 - quantengine - INFO - 开始执行回测计算...
2025-05-26 08:59:12 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 08:59:14 - quantengine - INFO - 回测完成，耗时:7.37秒，最终资产:-7730.65
2025-05-26 08:59:14 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 08:59:14 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 08:59:14 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 08:59:14 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 08:59:14 - quantengine - ERROR - 回测过程中发生错误: 'Portfolio' object has no attribute 'equity'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 74, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 113, in _create_plotly_dashboard
    self._add_equity_curve(fig, row=1, col=1)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 164, in _add_equity_curve
    equity = self.portfolio.equity()
             ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Portfolio' object has no attribute 'equity'
2025-05-26 09:02:36 - quantengine - INFO - 使用数据源: akshare
2025-05-26 09:02:36 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 09:02:36 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 09:02:36 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 09:02:36 - quantengine - INFO - 开始回测: 600000
2025-05-26 09:02:36 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 09:02:36 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 09:02:36 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 09:02:36 - quantengine - INFO - 开始执行策略计算...
2025-05-26 09:02:37 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 09:02:37 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 09:02:37 - quantengine - INFO - 开始执行回测计算...
2025-05-26 09:02:42 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 09:02:44 - quantengine - INFO - 回测完成，耗时:8.59秒，最终资产:-7730.65
2025-05-26 09:02:44 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 09:02:44 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 09:02:44 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 09:02:44 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 09:02:44 - quantengine - ERROR - 回测过程中发生错误: 'function' object has no attribute 'index'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 74, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 113, in _create_plotly_dashboard
    self._add_equity_curve(fig, row=1, col=1)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 182, in _add_equity_curve
    x=benchmark.index,
      ^^^^^^^^^^^^^^^
AttributeError: 'function' object has no attribute 'index'
2025-05-26 09:05:25 - quantengine - INFO - 使用数据源: akshare
2025-05-26 09:05:25 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 09:05:25 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 09:05:25 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 09:05:25 - quantengine - INFO - 开始回测: 600000
2025-05-26 09:05:25 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 09:05:25 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 09:05:25 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 09:05:25 - quantengine - INFO - 开始执行策略计算...
2025-05-26 09:05:26 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 09:05:26 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 09:05:26 - quantengine - INFO - 开始执行回测计算...
2025-05-26 09:05:32 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 09:05:34 - quantengine - INFO - 回测完成，耗时:9.11秒，最终资产:-7730.65
2025-05-26 09:05:34 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 09:05:34 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 09:05:34 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 09:05:34 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 09:05:34 - quantengine - ERROR - 回测过程中发生错误: 'Portfolio' object has no attribute 'entries'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 74, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 113, in _create_plotly_dashboard
    self._add_equity_curve(fig, row=1, col=1)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 192, in _add_equity_curve
    entries = self.portfolio.entries()
              ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Portfolio' object has no attribute 'entries'
2025-05-26 09:10:42 - quantengine - INFO - 使用数据源: akshare
2025-05-26 09:10:42 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 09:10:42 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 09:10:42 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 09:10:42 - quantengine - INFO - 开始回测: 600000
2025-05-26 09:10:42 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 09:10:42 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 09:10:42 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 09:10:42 - quantengine - INFO - 开始执行策略计算...
2025-05-26 09:10:43 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 09:10:43 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 09:10:43 - quantengine - INFO - 开始执行回测计算...
2025-05-26 09:10:47 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 09:10:49 - quantengine - INFO - 回测完成，耗时:6.72秒，最终资产:-7730.65
2025-05-26 09:10:49 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 09:10:49 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 09:10:49 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 09:10:49 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 09:10:49 - quantengine - ERROR - 回测过程中发生错误: 'ExitTrades' object has no attribute 'exit_time'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 74, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 122, in _create_plotly_dashboard
    self._add_trade_distribution(fig, row=2, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 415, in _add_trade_distribution
    x=trades.exit_time,
      ^^^^^^^^^^^^^^^^
AttributeError: 'ExitTrades' object has no attribute 'exit_time'. Did you mean: 'exit_idx'?
2025-05-26 09:23:41 - quantengine - INFO - 使用数据源: akshare
2025-05-26 09:23:41 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 09:23:41 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 09:23:41 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 09:23:41 - quantengine - INFO - 开始回测: 600000
2025-05-26 09:23:41 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 09:23:41 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 09:23:41 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 09:23:41 - quantengine - INFO - 开始执行策略计算...
2025-05-26 09:23:42 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 09:23:42 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 09:23:42 - quantengine - INFO - 开始执行回测计算...
2025-05-26 09:23:46 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 09:23:48 - quantengine - INFO - 回测完成，耗时:6.29秒，最终资产:-7730.65
2025-05-26 09:23:48 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 09:23:48 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 09:23:48 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 09:23:48 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 09:23:48 - quantengine - ERROR - 回测过程中发生错误: 'ExitTrades' object has no attribute 'exit_time'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 74, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 122, in _create_plotly_dashboard
    self._add_trade_distribution(fig, row=2, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 417, in _add_trade_distribution
    x=trades.exit_time,
      ^^^^^^^^^^^^^^^^
AttributeError: 'ExitTrades' object has no attribute 'exit_time'. Did you mean: 'exit_idx'?
2025-05-26 09:29:28 - quantengine - INFO - 使用数据源: akshare
2025-05-26 09:29:28 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 09:29:28 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 09:29:28 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 09:29:28 - quantengine - INFO - 开始回测: 600000
2025-05-26 09:29:28 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 09:29:28 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 09:29:28 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 09:29:28 - quantengine - INFO - 开始执行策略计算...
2025-05-26 09:29:29 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 09:29:29 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 09:29:29 - quantengine - INFO - 开始执行回测计算...
2025-05-26 09:29:32 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 09:29:34 - quantengine - INFO - 回测完成，耗时:6.59秒，最终资产:-7730.65
2025-05-26 09:29:34 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 09:29:34 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 09:29:34 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 09:29:34 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 09:29:35 - quantengine - ERROR - 回测过程中发生错误: Columns only: This object already contains one column of data
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 74, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 122, in _create_plotly_dashboard
    self._add_trade_distribution(fig, row=2, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 430, in _add_trade_distribution
    text=[f"收益: {ret:.2%}<br>持有: {dur}" for ret, dur in zip(returns, trades.duration)],
                                                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\base\indexing.py", line 145, in __getitem__
    return self.indexing_func(lambda x: x.__getitem__(key), **self.indexing_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\records\mapped_array.py", line 514, in indexing_func
    self.indexing_func_meta(pd_indexing_func, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\records\mapped_array.py", line 501, in indexing_func_meta
    self.wrapper.indexing_func_meta(pd_indexing_func, column_only_select=True, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\utils\decorators.py", line 443, in wrapper
    return cached_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\utils\decorators.py", line 413, in partial_func
    return func(instance, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\base\array_wrapper.py", line 217, in indexing_func_meta
    raise IndexingError("Columns only: This object already contains one column of data")
vectorbt.base.indexing.IndexingError: Columns only: This object already contains one column of data
2025-05-26 09:31:32 - quantengine - INFO - 使用数据源: akshare
2025-05-26 09:31:32 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 09:31:32 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 09:31:32 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 09:31:32 - quantengine - INFO - 开始回测: 600000
2025-05-26 09:31:32 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 09:31:32 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 09:31:32 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 09:31:32 - quantengine - INFO - 开始执行策略计算...
2025-05-26 09:31:33 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 09:31:33 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 09:31:33 - quantengine - INFO - 开始执行回测计算...
2025-05-26 09:31:38 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 09:31:41 - quantengine - INFO - 回测完成，耗时:8.73秒，最终资产:-7730.65
2025-05-26 09:31:41 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 09:31:41 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 09:31:41 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 09:31:41 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 09:31:41 - quantengine - ERROR - 回测过程中发生错误: Columns only: This object already contains one column of data
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 74, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 122, in _create_plotly_dashboard
    self._add_trade_distribution(fig, row=2, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 430, in _add_trade_distribution
    text=[f"收益: {ret:.2%}<br>持有: {trades.duration[i]}" for i, ret in enumerate(returns)],
                                                                         ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\base\indexing.py", line 145, in __getitem__
    return self.indexing_func(lambda x: x.__getitem__(key), **self.indexing_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\records\mapped_array.py", line 514, in indexing_func
    self.indexing_func_meta(pd_indexing_func, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\records\mapped_array.py", line 501, in indexing_func_meta
    self.wrapper.indexing_func_meta(pd_indexing_func, column_only_select=True, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\utils\decorators.py", line 443, in wrapper
    return cached_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\utils\decorators.py", line 413, in partial_func
    return func(instance, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\base\array_wrapper.py", line 217, in indexing_func_meta
    raise IndexingError("Columns only: This object already contains one column of data")
vectorbt.base.indexing.IndexingError: Columns only: This object already contains one column of data
2025-05-26 09:32:55 - quantengine - INFO - 使用数据源: akshare
2025-05-26 09:32:55 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 09:32:55 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 09:32:55 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 09:32:55 - quantengine - INFO - 开始回测: 600000
2025-05-26 09:32:55 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 09:32:55 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 09:32:55 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 09:32:55 - quantengine - INFO - 开始执行策略计算...
2025-05-26 09:32:56 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 09:32:56 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 09:32:56 - quantengine - INFO - 开始执行回测计算...
2025-05-26 09:33:01 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 09:33:03 - quantengine - INFO - 回测完成，耗时:8.30秒，最终资产:-7730.65
2025-05-26 09:33:03 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 09:33:03 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 09:33:03 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 09:33:03 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 09:33:03 - quantengine - ERROR - 回测过程中发生错误: Columns only: This object already contains one column of data
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 74, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 122, in _create_plotly_dashboard
    self._add_trade_distribution(fig, row=2, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 430, in _add_trade_distribution
    text=[f"收益: {ret:.2%}<br>持有: {trades.duration[i]}" for i, ret in enumerate(returns)],
                                                                         ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\base\indexing.py", line 145, in __getitem__
    return self.indexing_func(lambda x: x.__getitem__(key), **self.indexing_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\records\mapped_array.py", line 514, in indexing_func
    self.indexing_func_meta(pd_indexing_func, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\records\mapped_array.py", line 501, in indexing_func_meta
    self.wrapper.indexing_func_meta(pd_indexing_func, column_only_select=True, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\utils\decorators.py", line 443, in wrapper
    return cached_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\utils\decorators.py", line 413, in partial_func
    return func(instance, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\base\array_wrapper.py", line 217, in indexing_func_meta
    raise IndexingError("Columns only: This object already contains one column of data")
vectorbt.base.indexing.IndexingError: Columns only: This object already contains one column of data
2025-05-26 09:35:53 - quantengine - INFO - 使用数据源: akshare
2025-05-26 09:35:53 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 09:35:53 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 09:35:53 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 09:35:53 - quantengine - INFO - 开始回测: 600000
2025-05-26 09:35:53 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 09:35:53 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 09:35:53 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 09:35:53 - quantengine - INFO - 开始执行策略计算...
2025-05-26 09:35:54 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 09:35:54 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 09:35:54 - quantengine - INFO - 开始执行回测计算...
2025-05-26 09:36:00 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 09:36:03 - quantengine - INFO - 回测完成，耗时:10.26秒，最终资产:-7730.65
2025-05-26 09:36:03 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 09:36:03 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 09:36:03 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 09:36:03 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 09:36:03 - quantengine - ERROR - 回测过程中发生错误: no field of name duration
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 74, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 122, in _create_plotly_dashboard
    self._add_trade_distribution(fig, row=2, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 430, in _add_trade_distribution
    text=[f"收益: {ret:.2%}<br>持有: {dur}" for ret, dur in zip(returns, trades.records_arr['duration'])],
                                                                         ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
ValueError: no field of name duration
2025-05-26 09:41:34 - quantengine - INFO - 使用数据源: akshare
2025-05-26 09:41:34 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 09:41:34 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 09:41:34 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 09:41:34 - quantengine - INFO - 开始回测: 600000
2025-05-26 09:41:34 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 09:41:34 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 09:41:34 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 09:41:34 - quantengine - INFO - 开始执行策略计算...
2025-05-26 09:41:34 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 09:41:34 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 09:41:34 - quantengine - INFO - 开始执行回测计算...
2025-05-26 09:41:38 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 09:41:40 - quantengine - INFO - 回测完成，耗时:6.36秒，最终资产:-7730.65
2025-05-26 09:41:40 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 09:41:40 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 09:41:40 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 09:41:40 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 09:41:40 - quantengine - ERROR - 回测过程中发生错误: no field of name duration
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 74, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 122, in _create_plotly_dashboard
    self._add_trade_distribution(fig, row=2, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 430, in _add_trade_distribution
    text=[f"收益: {ret:.2%}<br>持有: {dur}" for ret, dur in zip(returns, trades.records_arr['duration'])],
                                                                         ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
ValueError: no field of name duration
2025-05-26 09:45:27 - quantengine - INFO - 使用数据源: akshare
2025-05-26 09:45:27 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 09:45:27 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 09:45:27 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 09:45:27 - quantengine - INFO - 开始回测: 600000
2025-05-26 09:45:27 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 09:45:27 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 09:45:27 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 09:45:27 - quantengine - INFO - 开始执行策略计算...
2025-05-26 09:45:27 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 09:45:27 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 09:45:27 - quantengine - INFO - 开始执行回测计算...
2025-05-26 09:45:31 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 09:45:33 - quantengine - INFO - 回测完成，耗时:6.77秒，最终资产:-7730.65
2025-05-26 09:45:33 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 09:45:33 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 09:45:33 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 09:45:33 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 09:45:34 - quantengine - ERROR - 回测过程中发生错误: Columns only: This object already contains one column of data
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 74, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 122, in _create_plotly_dashboard
    self._add_trade_distribution(fig, row=2, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 430, in _add_trade_distribution
    text=[f"收益: {ret:.2%}<br>持有: {trades.duration[i]}" for i, ret in enumerate(returns)],
                                                                         ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\base\indexing.py", line 145, in __getitem__
    return self.indexing_func(lambda x: x.__getitem__(key), **self.indexing_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\records\mapped_array.py", line 514, in indexing_func
    self.indexing_func_meta(pd_indexing_func, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\records\mapped_array.py", line 501, in indexing_func_meta
    self.wrapper.indexing_func_meta(pd_indexing_func, column_only_select=True, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\utils\decorators.py", line 443, in wrapper
    return cached_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\utils\decorators.py", line 413, in partial_func
    return func(instance, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\base\array_wrapper.py", line 217, in indexing_func_meta
    raise IndexingError("Columns only: This object already contains one column of data")
vectorbt.base.indexing.IndexingError: Columns only: This object already contains one column of data
2025-05-26 09:49:32 - quantengine - INFO - 使用数据源: akshare
2025-05-26 09:49:32 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 09:49:32 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 09:49:32 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 09:49:32 - quantengine - INFO - 开始回测: 600000
2025-05-26 09:49:32 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 09:49:32 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 09:49:32 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 09:49:32 - quantengine - INFO - 开始执行策略计算...
2025-05-26 09:49:33 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 09:49:33 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 09:49:33 - quantengine - INFO - 开始执行回测计算...
2025-05-26 09:49:38 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 09:49:40 - quantengine - INFO - 回测完成，耗时:7.86秒，最终资产:-7730.65
2025-05-26 09:49:40 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 09:49:40 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 09:49:40 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 09:49:40 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 09:49:40 - quantengine - ERROR - 回测过程中发生错误: Columns only: This object already contains one column of data
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 74, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 122, in _create_plotly_dashboard
    self._add_trade_distribution(fig, row=2, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 433, in _add_trade_distribution
    text=[f"收益: {ret:.2%}<br>持有: {durations[i]}" for i, ret in enumerate(returns)],
                                                                   ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\base\indexing.py", line 145, in __getitem__
    return self.indexing_func(lambda x: x.__getitem__(key), **self.indexing_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\records\mapped_array.py", line 514, in indexing_func
    self.indexing_func_meta(pd_indexing_func, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\records\mapped_array.py", line 501, in indexing_func_meta
    self.wrapper.indexing_func_meta(pd_indexing_func, column_only_select=True, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\utils\decorators.py", line 443, in wrapper
    return cached_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\utils\decorators.py", line 413, in partial_func
    return func(instance, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\base\array_wrapper.py", line 217, in indexing_func_meta
    raise IndexingError("Columns only: This object already contains one column of data")
vectorbt.base.indexing.IndexingError: Columns only: This object already contains one column of data
2025-05-26 09:50:40 - quantengine - INFO - 使用数据源: akshare
2025-05-26 09:50:40 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 09:50:40 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 09:50:40 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 09:50:40 - quantengine - INFO - 开始回测: 600000
2025-05-26 09:50:40 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 09:50:40 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 09:50:40 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 09:50:40 - quantengine - INFO - 开始执行策略计算...
2025-05-26 09:50:41 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 09:50:41 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 09:50:41 - quantengine - INFO - 开始执行回测计算...
2025-05-26 09:50:45 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 09:50:47 - quantengine - INFO - 回测完成，耗时:6.27秒，最终资产:-7730.65
2025-05-26 09:50:47 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 09:50:47 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 09:50:47 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 09:50:47 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 09:50:47 - quantengine - ERROR - 回测过程中发生错误: Columns only: This object already contains one column of data
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 74, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 122, in _create_plotly_dashboard
    self._add_trade_distribution(fig, row=2, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 433, in _add_trade_distribution
    text=[f"收益: {ret:.2%}<br>持有: {durations[i]}" for i, ret in enumerate(returns)],
                                                                   ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\base\indexing.py", line 145, in __getitem__
    return self.indexing_func(lambda x: x.__getitem__(key), **self.indexing_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\records\mapped_array.py", line 514, in indexing_func
    self.indexing_func_meta(pd_indexing_func, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\records\mapped_array.py", line 501, in indexing_func_meta
    self.wrapper.indexing_func_meta(pd_indexing_func, column_only_select=True, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\utils\decorators.py", line 443, in wrapper
    return cached_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\utils\decorators.py", line 413, in partial_func
    return func(instance, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\base\array_wrapper.py", line 217, in indexing_func_meta
    raise IndexingError("Columns only: This object already contains one column of data")
vectorbt.base.indexing.IndexingError: Columns only: This object already contains one column of data
2025-05-26 09:53:37 - quantengine - INFO - 使用数据源: akshare
2025-05-26 09:53:37 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 09:53:37 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 09:53:37 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 09:53:37 - quantengine - INFO - 开始回测: 600000
2025-05-26 09:53:37 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 09:53:37 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 09:53:37 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 09:53:37 - quantengine - INFO - 开始执行策略计算...
2025-05-26 09:53:38 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 09:53:38 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 09:53:38 - quantengine - INFO - 开始执行回测计算...
2025-05-26 09:53:42 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 09:53:44 - quantengine - INFO - 回测完成，耗时:6.30秒，最终资产:-7730.65
2025-05-26 09:53:44 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 09:53:44 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 09:53:44 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 09:53:44 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 09:53:46 - quantengine - ERROR - 回测过程中发生错误: 'ExitTrades' object has no attribute 'entry_time'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 74, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 128, in _create_plotly_dashboard
    self._add_trade_details_table(fig, row=3, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 622, in _add_trade_details_table
    '入场时间': trades.entry_time.dt.strftime('%Y-%m-%d'),
                ^^^^^^^^^^^^^^^^^
AttributeError: 'ExitTrades' object has no attribute 'entry_time'. Did you mean: 'entry_idx'?
2025-05-26 09:57:20 - quantengine - INFO - 使用数据源: akshare
2025-05-26 09:57:20 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 09:57:20 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 09:57:20 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 09:57:20 - quantengine - INFO - 开始回测: 600000
2025-05-26 09:57:20 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 09:57:20 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 09:57:20 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 09:57:20 - quantengine - INFO - 开始执行策略计算...
2025-05-26 09:57:21 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 09:57:21 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 09:57:21 - quantengine - INFO - 开始执行回测计算...
2025-05-26 09:57:25 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 09:57:27 - quantengine - INFO - 回测完成，耗时:6.53秒，最终资产:-7730.65
2025-05-26 09:57:27 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 09:57:27 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 09:57:27 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 09:57:27 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 09:57:29 - quantengine - ERROR - 回测过程中发生错误: 'MappedArray' object has no attribute 'dt'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 74, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 128, in _create_plotly_dashboard
    self._add_trade_details_table(fig, row=3, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 622, in _add_trade_details_table
    '入场时间': trades.entry_idx.dt.strftime('%Y-%m-%d'),
                ^^^^^^^^^^^^^^^^^^^
AttributeError: 'MappedArray' object has no attribute 'dt'
2025-05-26 10:01:12 - quantengine - INFO - 使用数据源: akshare
2025-05-26 10:01:12 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 10:01:12 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 10:01:12 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 10:01:12 - quantengine - INFO - 开始回测: 600000
2025-05-26 10:01:12 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 10:01:12 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 10:01:12 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 10:01:12 - quantengine - INFO - 开始执行策略计算...
2025-05-26 10:01:13 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 10:01:13 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 10:01:13 - quantengine - INFO - 开始执行回测计算...
2025-05-26 10:01:17 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 10:01:19 - quantengine - INFO - 回测完成，耗时:6.55秒，最终资产:-7730.65
2025-05-26 10:01:19 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 10:01:19 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 10:01:19 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 10:01:19 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 10:01:21 - quantengine - ERROR - 回测过程中发生错误: Columns only: This object already contains one column of data
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 74, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 128, in _create_plotly_dashboard
    self._add_trade_details_table(fig, row=3, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 622, in _add_trade_details_table
    '入场时间': pd.to_datetime(trades.entry_idx).strftime('%Y-%m-%d'),
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\tools\datetimes.py", line 1146, in to_datetime
    result = convert_listlike(np.array([arg]), format)[0]
                              ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\base\indexing.py", line 145, in __getitem__
    return self.indexing_func(lambda x: x.__getitem__(key), **self.indexing_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\records\mapped_array.py", line 514, in indexing_func
    self.indexing_func_meta(pd_indexing_func, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\records\mapped_array.py", line 501, in indexing_func_meta
    self.wrapper.indexing_func_meta(pd_indexing_func, column_only_select=True, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\utils\decorators.py", line 443, in wrapper
    return cached_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\utils\decorators.py", line 413, in partial_func
    return func(instance, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\base\array_wrapper.py", line 217, in indexing_func_meta
    raise IndexingError("Columns only: This object already contains one column of data")
vectorbt.base.indexing.IndexingError: Columns only: This object already contains one column of data
2025-05-26 10:03:30 - quantengine - INFO - 使用数据源: akshare
2025-05-26 10:03:30 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 10:03:30 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 10:03:30 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 10:03:30 - quantengine - INFO - 开始回测: 600000
2025-05-26 10:03:30 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 10:03:30 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 10:03:30 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 10:03:30 - quantengine - INFO - 开始执行策略计算...
2025-05-26 10:03:30 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 10:03:30 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 10:03:30 - quantengine - INFO - 开始执行回测计算...
2025-05-26 10:03:34 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 10:03:36 - quantengine - INFO - 回测完成，耗时:6.22秒，最终资产:-7730.65
2025-05-26 10:03:36 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 10:03:36 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 10:03:36 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 10:03:36 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 10:03:37 - quantengine - ERROR - 回测过程中发生错误: 'MappedArray' object has no attribute 'dt'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 74, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 128, in _create_plotly_dashboard
    self._add_trade_details_table(fig, row=3, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 624, in _add_trade_details_table
    '持有天数': trades.duration.dt.days,
                ^^^^^^^^^^^^^^^^^^
AttributeError: 'MappedArray' object has no attribute 'dt'
2025-05-26 10:07:00 - quantengine - INFO - 使用数据源: akshare
2025-05-26 10:07:00 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 10:07:00 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 10:07:00 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 10:07:00 - quantengine - INFO - 开始回测: 600000
2025-05-26 10:07:00 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 10:07:00 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 10:07:00 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 10:07:00 - quantengine - INFO - 开始执行策略计算...
2025-05-26 10:07:01 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 10:07:01 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 10:07:01 - quantengine - INFO - 开始执行回测计算...
2025-05-26 10:07:05 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 10:07:07 - quantengine - INFO - 回测完成，耗时:6.19秒，最终资产:-7730.65
2025-05-26 10:07:07 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 10:07:07 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 10:07:07 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 10:07:07 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 10:07:08 - quantengine - ERROR - 回测过程中发生错误: 'EnhancedReportGenerator' object has no attribute '_engine'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 74, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 128, in _create_plotly_dashboard
    self._add_trade_details_table(fig, row=3, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 594, in _add_trade_details_table
    trade_records = self._engine.get_all_trades()
                    ^^^^^^^^^^^^
AttributeError: 'EnhancedReportGenerator' object has no attribute '_engine'
2025-05-26 10:09:38 - quantengine - INFO - 使用数据源: akshare
2025-05-26 10:09:38 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 10:09:38 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 10:09:38 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 10:09:38 - quantengine - INFO - 开始回测: 600000
2025-05-26 10:09:38 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 10:09:38 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 10:09:38 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 10:09:38 - quantengine - INFO - 开始执行策略计算...
2025-05-26 10:09:39 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 10:09:39 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 10:09:39 - quantengine - INFO - 开始执行回测计算...
2025-05-26 10:09:42 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 10:09:44 - quantengine - INFO - 回测完成，耗时:6.19秒，最终资产:-7730.65
2025-05-26 10:09:44 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 10:09:44 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 10:09:44 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 10:09:44 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 10:09:44 - quantengine - ERROR - 回测过程中发生错误: EnhancedReportGenerator.__init__() got an unexpected keyword argument 'metrics'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 311, in main
    report_generator = EnhancedReportGenerator(
                       ^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: EnhancedReportGenerator.__init__() got an unexpected keyword argument 'metrics'
2025-05-26 10:11:29 - quantengine - INFO - 使用数据源: akshare
2025-05-26 10:11:29 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 10:11:29 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 10:11:29 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 10:11:29 - quantengine - INFO - 开始回测: 600000
2025-05-26 10:11:29 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 10:11:29 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 10:11:29 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 10:11:29 - quantengine - INFO - 开始执行策略计算...
2025-05-26 10:11:30 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 10:11:30 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 10:11:30 - quantengine - INFO - 开始执行回测计算...
2025-05-26 10:11:34 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 10:11:35 - quantengine - INFO - 回测完成，耗时:6.30秒，最终资产:-7730.65
2025-05-26 10:11:35 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 10:11:35 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 10:11:35 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 10:11:35 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 10:11:37 - quantengine - ERROR - 回测过程中发生错误: 'NoneType' object has no attribute 'get_all_trades'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 72, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 126, in _create_plotly_dashboard
    self._add_trade_details_table(fig, row=3, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 592, in _add_trade_details_table
    trade_records = self._engine.get_all_trades()
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get_all_trades'
2025-05-26 10:13:29 - quantengine - INFO - 使用数据源: akshare
2025-05-26 10:13:29 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 10:13:29 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 10:13:29 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 10:13:29 - quantengine - INFO - 开始回测: 600000
2025-05-26 10:13:29 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 10:13:29 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 10:13:29 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 10:13:29 - quantengine - INFO - 开始执行策略计算...
2025-05-26 10:13:30 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 10:13:30 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 10:13:30 - quantengine - INFO - 开始执行回测计算...
2025-05-26 10:13:34 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 10:13:35 - quantengine - INFO - 回测完成，耗时:6.18秒，最终资产:-7730.65
2025-05-26 10:13:35 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 10:13:35 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 10:13:35 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 10:13:35 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 10:13:37 - quantengine - ERROR - 回测过程中发生错误: 'EnhancedBacktestEngine' object has no attribute 'get_all_trades'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 324, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 72, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 126, in _create_plotly_dashboard
    self._add_trade_details_table(fig, row=3, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 592, in _add_trade_details_table
    trade_records = self._engine.get_all_trades()
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'EnhancedBacktestEngine' object has no attribute 'get_all_trades'
2025-05-26 10:15:07 - quantengine - INFO - 使用数据源: akshare
2025-05-26 10:15:07 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 10:15:07 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 10:15:07 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 10:15:07 - quantengine - INFO - 开始回测: 600000
2025-05-26 10:15:07 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 10:15:07 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 10:15:07 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 10:15:07 - quantengine - INFO - 开始执行策略计算...
2025-05-26 10:15:08 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 10:15:08 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 10:15:08 - quantengine - INFO - 开始执行回测计算...
2025-05-26 10:15:12 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 10:15:15 - quantengine - INFO - 回测完成，耗时:7.94秒，最终资产:-7730.65
2025-05-26 10:15:15 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 10:15:15 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 10:15:15 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 10:15:15 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 10:15:17 - quantengine - ERROR - 回测过程中发生错误: 'EnhancedBacktestEngine' object has no attribute 'get_all_trades'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 324, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 72, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 126, in _create_plotly_dashboard
    self._add_trade_details_table(fig, row=3, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 592, in _add_trade_details_table
    trade_records = self._engine.get_all_trades()
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'EnhancedBacktestEngine' object has no attribute 'get_all_trades'
2025-05-26 10:19:50 - quantengine - INFO - 使用数据源: akshare
2025-05-26 10:19:50 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 10:19:50 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 10:19:50 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 10:19:50 - quantengine - INFO - 开始回测: 600000
2025-05-26 10:19:50 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 10:19:50 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 10:19:50 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 10:19:50 - quantengine - INFO - 开始执行策略计算...
2025-05-26 10:19:51 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 10:19:51 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 10:19:51 - quantengine - INFO - 开始执行回测计算...
2025-05-26 10:19:56 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 10:19:58 - quantengine - INFO - 回测完成，耗时:7.41秒，最终资产:-7730.65
2025-05-26 10:19:58 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 10:19:58 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 10:19:58 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 10:19:58 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 10:19:58 - quantengine - ERROR - 回测过程中发生错误: 
Cannot add annotation to subplot at position (3, 2) because subplot
is of type domain.
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 616, in _add_trade_details_table
    trade_records = self._engine.get_all_trades()
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_engine.py", line 521, in get_all_trades
    if hasattr(self.portfolio, 'trades') and self.portfolio.trades.count() > 0:
               ^^^^^^^^^^^^^^
AttributeError: 'EnhancedBacktestEngine' object has no attribute 'portfolio'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 324, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 72, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 126, in _create_plotly_dashboard
    self._add_trade_details_table(fig, row=3, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 654, in _add_trade_details_table
    fig.add_annotation(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\plotly\graph_objs\_figure.py", line 24549, in add_annotation
    return self._add_annotation_like(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\plotly\basedatatypes.py", line 1550, in _add_annotation_like
    raise ValueError(
ValueError: 
Cannot add annotation to subplot at position (3, 2) because subplot
is of type domain.
2025-05-26 10:26:41 - quantengine - INFO - 使用数据源: akshare
2025-05-26 10:26:41 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 10:26:41 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 10:26:41 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 10:26:41 - quantengine - INFO - 开始回测: 600000
2025-05-26 10:26:41 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 10:26:41 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 10:26:41 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 10:26:41 - quantengine - INFO - 开始执行策略计算...
2025-05-26 10:26:43 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 10:26:43 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 10:26:43 - quantengine - INFO - 开始执行回测计算...
2025-05-26 10:26:48 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 10:26:50 - quantengine - INFO - 回测完成，耗时:8.56秒，最终资产:-7730.65
2025-05-26 10:26:50 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 10:26:50 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 10:26:50 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 10:26:50 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 10:26:50 - quantengine - ERROR - 回测过程中发生错误: 'EnhancedReportGenerator' object has no attribute 'logger'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 383, in _add_trade_distribution
    trades = self._engine.get_all_trades()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_engine.py", line 530, in get_all_trades
    'entry_time': self.portfolio.trades.entry_idx[i],
                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\base\indexing.py", line 145, in __getitem__
    return self.indexing_func(lambda x: x.__getitem__(key), **self.indexing_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\records\mapped_array.py", line 514, in indexing_func
    self.indexing_func_meta(pd_indexing_func, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\records\mapped_array.py", line 501, in indexing_func_meta
    self.wrapper.indexing_func_meta(pd_indexing_func, column_only_select=True, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\utils\decorators.py", line 443, in wrapper
    return cached_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\utils\decorators.py", line 413, in partial_func
    return func(instance, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\base\array_wrapper.py", line 217, in indexing_func_meta
    raise IndexingError("Columns only: This object already contains one column of data")
vectorbt.base.indexing.IndexingError: Columns only: This object already contains one column of data

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 324, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 72, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 120, in _create_plotly_dashboard
    self._add_trade_distribution(fig, row=2, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 467, in _add_trade_distribution
    self.logger.error(f"生成交易分布图时出错: {e}")
    ^^^^^^^^^^^
AttributeError: 'EnhancedReportGenerator' object has no attribute 'logger'
2025-05-26 10:30:25 - quantengine - INFO - 使用数据源: akshare
2025-05-26 10:30:25 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 10:30:25 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 10:30:25 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 10:30:25 - quantengine - INFO - 开始回测: 600000
2025-05-26 10:30:25 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 10:30:25 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 10:30:25 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 10:30:25 - quantengine - INFO - 开始执行策略计算...
2025-05-26 10:30:26 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 10:30:26 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 10:30:26 - quantengine - INFO - 开始执行回测计算...
2025-05-26 10:30:31 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 10:30:33 - quantengine - INFO - 回测完成，耗时:8.14秒，最终资产:-7730.65
2025-05-26 10:30:33 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 10:30:33 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 10:30:33 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 10:30:33 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 10:30:34 - quantengine.backtest.enhanced_report - ERROR - 生成交易分布图时出错: Columns only: This object already contains one column of data
2025-05-26 10:30:34 - quantengine - ERROR - 回测过程中发生错误: 'Figure' object has no attribute 'get_subplot_xref'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 388, in _add_trade_distribution
    trades = self._engine.get_all_trades()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_engine.py", line 530, in get_all_trades
    'entry_time': self.portfolio.trades.entry_idx[i],
                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\base\indexing.py", line 145, in __getitem__
    return self.indexing_func(lambda x: x.__getitem__(key), **self.indexing_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\records\mapped_array.py", line 514, in indexing_func
    self.indexing_func_meta(pd_indexing_func, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\records\mapped_array.py", line 501, in indexing_func_meta
    self.wrapper.indexing_func_meta(pd_indexing_func, column_only_select=True, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\utils\decorators.py", line 443, in wrapper
    return cached_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\utils\decorators.py", line 413, in partial_func
    return func(instance, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\vectorbt\base\array_wrapper.py", line 217, in indexing_func_meta
    raise IndexingError("Columns only: This object already contains one column of data")
vectorbt.base.indexing.IndexingError: Columns only: This object already contains one column of data

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 324, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 77, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 125, in _create_plotly_dashboard
    self._add_trade_distribution(fig, row=2, col=2)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 476, in _add_trade_distribution
    xref=f"x{fig.get_subplot_xref(row, col)}",
             ^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Figure' object has no attribute 'get_subplot_xref'. Did you mean: 'get_subplot'?
2025-05-26 10:31:47 - quantengine - INFO - 使用数据源: akshare
2025-05-26 10:31:47 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 10:31:47 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 10:31:47 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 10:31:47 - quantengine - INFO - 开始回测: 600000
2025-05-26 10:31:47 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 10:31:47 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 10:31:47 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 10:31:47 - quantengine - INFO - 开始执行策略计算...
2025-05-26 10:31:48 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 10:31:48 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 10:31:48 - quantengine - INFO - 开始执行回测计算...
2025-05-26 10:31:53 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 10:31:55 - quantengine - INFO - 回测完成，耗时:8.55秒，最终资产:-7730.65
2025-05-26 10:31:55 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 10:31:55 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 10:31:55 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 10:31:55 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 10:31:55 - quantengine.backtest.enhanced_report - ERROR - 生成交易分布图时出错: Columns only: This object already contains one column of data
2025-05-26 10:31:55 - quantengine - ERROR - 回测过程中发生错误: 'EnhancedReportGenerator' object has no attribute '_add_rolling_metrics'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 324, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 77, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 128, in _create_plotly_dashboard
    self._add_rolling_metrics(fig, row=3, col=1)
    ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'EnhancedReportGenerator' object has no attribute '_add_rolling_metrics'
2025-05-26 10:33:52 - quantengine - INFO - 使用数据源: akshare
2025-05-26 10:33:52 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 10:33:52 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 10:33:52 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 10:33:52 - quantengine - INFO - 开始回测: 600000
2025-05-26 10:33:52 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 10:33:52 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 10:33:52 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 10:33:52 - quantengine - INFO - 开始执行策略计算...
2025-05-26 10:33:53 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 10:33:53 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 10:33:53 - quantengine - INFO - 开始执行回测计算...
2025-05-26 10:33:59 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 10:34:01 - quantengine - INFO - 回测完成，耗时:8.99秒，最终资产:-7730.65
2025-05-26 10:34:01 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 10:34:01 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 10:34:01 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 10:34:01 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 10:34:01 - quantengine.backtest.enhanced_report - ERROR - 生成交易分布图时出错: Columns only: This object already contains one column of data
2025-05-26 10:34:01 - quantengine - ERROR - 回测过程中发生错误: 'EnhancedReportGenerator' object has no attribute 'returns'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 324, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 77, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 128, in _create_plotly_dashboard
    self._add_rolling_metrics(fig, row=3, col=1)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 780, in _add_rolling_metrics
    rolling_returns = self.returns.rolling(30).mean()
                      ^^^^^^^^^^^^
AttributeError: 'EnhancedReportGenerator' object has no attribute 'returns'
2025-05-26 10:35:20 - quantengine - INFO - 使用数据源: akshare
2025-05-26 10:35:20 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 10:35:20 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 10:35:20 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 10:35:20 - quantengine - INFO - 开始回测: 600000
2025-05-26 10:35:20 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 10:35:20 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 10:35:20 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 10:35:20 - quantengine - INFO - 开始执行策略计算...
2025-05-26 10:35:21 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 10:35:21 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 10:35:21 - quantengine - INFO - 开始执行回测计算...
2025-05-26 10:35:26 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 10:35:28 - quantengine - INFO - 回测完成，耗时:8.78秒，最终资产:-7730.65
2025-05-26 10:35:28 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 10:35:28 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 10:35:28 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 10:35:28 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 10:35:29 - quantengine.backtest.enhanced_report - ERROR - 生成交易分布图时出错: Columns only: This object already contains one column of data
2025-05-26 10:35:29 - quantengine - ERROR - 回测过程中发生错误: 'EnhancedReportGenerator' object has no attribute 'returns'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 324, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 77, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 128, in _create_plotly_dashboard
    self._add_rolling_metrics(fig, row=3, col=1)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 780, in _add_rolling_metrics
    rolling_returns = self.returns.rolling(30).mean()
                      ^^^^^^^^^^^^
AttributeError: 'EnhancedReportGenerator' object has no attribute 'returns'
2025-05-26 11:16:29 - quantengine - INFO - 使用数据源: akshare
2025-05-26 11:16:29 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-26 11:16:29 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-26 11:16:29 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-26 11:16:29 - quantengine - INFO - 开始回测: 600000
2025-05-26 11:16:29 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-26 11:16:29 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-26 11:16:29 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-26 11:16:29 - quantengine - INFO - 开始执行策略计算...
2025-05-26 11:16:30 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-26 11:16:30 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-26 11:16:30 - quantengine - INFO - 开始执行回测计算...
2025-05-26 11:16:38 - quantengine - INFO - 生成了 7 条交易记录
2025-05-26 11:16:41 - quantengine - INFO - 回测完成，耗时:12.33秒，最终资产:-7730.65
2025-05-26 11:16:41 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-26 11:16:41 - quantengine - INFO - 年化收益率: -7.73%
2025-05-26 11:16:41 - quantengine - INFO - 夏普比率: -0.82
2025-05-26 11:16:41 - quantengine - INFO - 最大回撤: -14.30%
2025-05-26 11:16:41 - quantengine.backtest.enhanced_report - ERROR - 生成交易分布图时出错: Columns only: This object already contains one column of data
2025-05-26 11:16:41 - quantengine - ERROR - 回测过程中发生错误: 'EnhancedReportGenerator' object has no attribute 'returns'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 324, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 78, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 129, in _create_plotly_dashboard
    self._add_rolling_metrics(fig, row=3, col=1)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 796, in _add_rolling_metrics
    rolling_returns = self.returns.rolling(30).mean()
                      ^^^^^^^^^^^^
AttributeError: 'EnhancedReportGenerator' object has no attribute 'returns'
