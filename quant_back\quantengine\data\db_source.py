import pandas as pd
import sqlite3
from sqlalchemy import create_engine
from .data_source import DataSource

class DBSource(DataSource):
    """基于数据库的数据源实现"""
    
    def __init__(self, connection_string="sqlite:///market_data.db"):
        """
        初始化数据库数据源
        
        参数:
            connection_string (str): 数据库连接字符串
        """
        self.connection_string = connection_string
        self.engine = create_engine(connection_string)
    
    def get_data(self, symbol, start_date, end_date, timeframe='1d'):
        """从数据库获取数据"""
        # 根据时间周期选择不同的表
        table_name = f"market_data_{timeframe}"
        
        # 构建查询
        query = f"""
        SELECT * FROM {table_name}
        WHERE symbol = '{symbol}'
        AND date >= '{start_date}'
        AND date <= '{end_date}'
        ORDER BY date
        """
        
        # 执行查询
        df = pd.read_sql(query, self.engine)
        
        # 标准化列名
        df = self._standardize_columns(df)
        
        return df
    
    def _standardize_columns(self, df):
        """标准化数据列名"""
        # 映射数据库列名到标准列名
        column_mapping = {
            'date': 'date',
            'open_price': 'open',
            'high_price': 'high',
            'low_price': 'low',
            'close_price': 'close',
            'volume': 'volume',
            'turnover': 'turnover'
        }
        
        # 重命名列
        df = df.rename(columns={k: v for k, v in column_mapping.items() if k in df.columns})
        
        # 确保日期列是日期时间类型
        df['date'] = pd.to_datetime(df['date'])
        df.set_index('date', inplace=True)
        
        return df