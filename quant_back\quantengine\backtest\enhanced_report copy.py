import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import vectorbt as vbt
from typing import Dict, List, Optional, Union, Any, Tuple
from pathlib import Path
import os
import datetime
import json
from ..config import ConfigManager
import logging  # 导入日志模块

# 获取日志器
logger = logging.getLogger(__name__)


class EnhancedReportGenerator:
    def __init__(self, portfolio, symbol, strategy_name, params, start_date, end_date, metrics=None, engine=None):
        """
        初始化报告生成器

        参数:
            portfolio (vbt.Portfolio): 回测结果
            symbol (str): 证券代码
            strategy_name (str): 策略名称
            params (dict): 策略参数
            start_date (str): 开始日期
            end_date (str): 结束日期
            metrics (dict): 绩效指标数据，默认为None
            engine (object): 引擎实例，用于获取交易记录，默认为None
        """
        self.portfolio = portfolio
        self.metrics = metrics if metrics is not None else {}
        self.symbol = symbol
        self.strategy_name = strategy_name
        self.params = params
        self.start_date = start_date
        self.end_date = end_date
        self._engine = engine  # 保存引擎实例
        
        # 计算并存储收益率
        self.returns = portfolio.returns()

        # 获取配置
        self.config = ConfigManager()

        # 设置输出目录
        output_dir = self.config.get("report.output_dir", "output")
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 设置样式
        sns.set(style="whitegrid")
        plt.rcParams['figure.figsize'] = (12, 8)
        self.logger = logger  # 初始化日志器

    def generate_html_report(self, filename: Optional[str] = None) -> str:
        """
        生成HTML报告

        参数:
            filename (str): 文件名，如果为None则自动生成

        返回:
            str: 报告文件路径
        """
        # 生成文件名
        if filename is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            symbol_clean = self.symbol.replace('/', '_').replace(':', '_')
            filename = f"{self.strategy_name.lower()}_{symbol_clean}_{timestamp}.html"

        # 完整文件路径
        file_path = self.output_dir / filename

        # 创建Plotly图表
        fig = self._create_plotly_dashboard()

        # 保存为HTML文件
        fig.write_html(
            file_path,
            include_plotlyjs='cdn',
            full_html=True,
            config={'displayModeBar': True, 'scrollZoom': True}
        )

        return str(file_path)

    def _create_plotly_dashboard(self) -> go.Figure:
        """
        创建Plotly仪表板

        返回:
            go.Figure: Plotly图表
        """
        # 创建子图
        fig = make_subplots(
            rows=4, cols=2,
            subplot_titles=(
                "资金曲线", "回撤分析",
                "月度收益热图", "交易分布",
                "滚动绩效指标", "交易详情",
                "收益分布", "策略参数"
            ),
            specs=[
                [{"type": "xy"}, {"type": "xy"}],
                [{"type": "xy"}, {"type": "xy"}],
                [{"type": "table"}, {"type": "table"}],
                [{"type": "xy"}, {"type": "table"}]
            ],
            vertical_spacing=0.1,
            horizontal_spacing=0.05
        )

        # 1. 资金曲线
        self._add_equity_curve(fig, row=1, col=1)

        # 2. 回撤分析
        self._add_drawdown_analysis(fig, row=1, col=2)

        # 3. 月度收益热图
        self._add_monthly_returns_heatmap(fig, row=2, col=1)

        # 4. 交易分布
        self._add_trade_distribution(fig, row=2, col=2)

        # 5. 交易详情表格
        self._add_trade_details_table(fig, row=3, col=2)

        # 6. 收益分布
        self._add_returns_distribution(fig, row=4, col=1)

        # 7. 滚动绩效指标
        self._add_rolling_metrics(fig, row=4, col=1)

        # 8. 策略参数表格
        self._add_strategy_params_table(fig, row=4, col=2)

        # 设置标题和布局
        title_text = f"{self.strategy_name} - {self.symbol} ({self.start_date} 至 {self.end_date})"
        fig.update_layout(
            title={
                'text': title_text,
                'y': 0.98,
                'x': 0.5,
                'xanchor': 'center',
                'yanchor': 'top',
                'font': {'size': 24}
            },
            height=1800,
            width=1200,
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )

        return fig

    def _add_equity_curve(self, fig: go.Figure, row: int, col: int):
        """添加资金曲线"""
        # 获取资金曲线数据
        equity = self.portfolio.value()
        benchmark = self.portfolio.benchmark_value()

        # 添加资金曲线
        fig.add_trace(
            go.Scatter(
                x=equity.index,
                y=equity.values,
                mode='lines',
                name='策略资金曲线',
                line=dict(color='blue', width=2)
            ),
            row=row, col=col
        )

        # 添加基准曲线
        fig.add_trace(
            go.Scatter(
                x=benchmark.index,
                y=benchmark.values,
                mode='lines',
                name='基准资金曲线',
                line=dict(color='gray', width=1, dash='dash')
            ),
            row=row, col=col
        )

        # 添加买入点
        if hasattr(self.portfolio.orders, 'records'):
            buy_records = self.portfolio.orders.records[self.portfolio.orders.records['side'] == 'buy']
            if not buy_records.empty:
                fig.add_trace(
                    go.Scatter(
                        x=buy_records['index'],
                        y=equity.loc[buy_records['index']],
                        mode='markers',
                        name='买入点',
                        marker=dict(color='green', size=8,
                                    symbol='triangle-up')
                    ),
                    row=row, col=col
                )

        # 添加卖出点
        if hasattr(self.portfolio.orders, 'records'):
            sell_records = self.portfolio.orders.records[self.portfolio.orders.records['side'] == 'sell']
            if not sell_records.empty:
                fig.add_trace(
                    go.Scatter(
                        x=sell_records['index'],
                        y=equity.loc[sell_records['index']],
                        mode='markers',
                        name='卖出点',
                        marker=dict(color='red', size=8,
                                    symbol='triangle-down')
                    ),
                    row=row, col=col
                )

        # 更新轴标签
        fig.update_xaxes(title_text='日期', row=row, col=col)
        fig.update_yaxes(title_text='资金', row=row, col=col)

        # 添加性能指标注释
        annotations = [
            f"总收益率: {self.metrics['total_return']:.2%}",
            f"年化收益率: {self.metrics['annual_return']:.2%}",
            f"夏普比率: {self.metrics['sharpe_ratio']:.2f}",
            f"最大回撤: {self.metrics['max_drawdown']:.2%}",
            f"胜率: {self.metrics['win_rate']:.2%}"
        ]

        annotation_text = "<br>".join(annotations)

        fig.add_annotation(
            xref="x domain",
            yref="y domain",
            x=0.01,
            y=0.99,
            text=annotation_text,
            showarrow=False,
            font=dict(size=12),
            align="left",
            bgcolor="rgba(255, 255, 255, 0.8)",
            bordercolor="black",
            borderwidth=1,
            borderpad=4,
            row=row, col=col
        )

    def _add_drawdown_analysis(self, fig: go.Figure, row: int, col: int):
        """添加回撤分析"""
        # 计算回撤
        drawdown = self.portfolio.drawdown()

        # 添加回撤曲线
        fig.add_trace(
            go.Scatter(
                x=drawdown.index,
                y=drawdown.values * 100,  # 转换为百分比
                mode='lines',
                name='回撤',
                line=dict(color='red', width=2),
                fill='tozeroy'
            ),
            row=row, col=col
        )

        # 更新轴标签
        fig.update_xaxes(title_text='日期', row=row, col=col)
        fig.update_yaxes(title_text='回撤 (%)', row=row, col=col)

        # 添加最大回撤标记
        max_dd_idx = drawdown.idxmin()
        max_dd = drawdown.min()

        fig.add_trace(
            go.Scatter(
                x=[max_dd_idx],
                y=[max_dd * 100],
                mode='markers+text',
                name='最大回撤',
                marker=dict(color='darkred', size=10),
                text=[f"{max_dd:.2%}"],
                textposition="bottom center"
            ),
            row=row, col=col
        )

        # 添加回撤统计注释
        underwater_periods = self._calculate_underwater_periods(drawdown)

        annotations = [
            f"最大回撤: {max_dd:.2%}",
            f"平均回撤: {drawdown[drawdown < 0].mean():.2%}",
            f"回撤次数: {len(underwater_periods)}",
            f"平均回撤持续天数: {np.mean([len(period) for period in underwater_periods]):.1f}",
            f"最长回撤持续天数: {max([len(period) for period in underwater_periods]) if underwater_periods else 0}"
        ]

        annotation_text = "<br>".join(annotations)

        fig.add_annotation(
            xref="x domain",
            yref="y domain",
            x=0.01,
            y=0.99,
            text=annotation_text,
            showarrow=False,
            font=dict(size=12),
            align="left",
            bgcolor="rgba(255, 255, 255, 0.8)",
            bordercolor="black",
            borderwidth=1,
            borderpad=4,
            row=row, col=col
        )

    def _calculate_underwater_periods(self, drawdown: pd.Series) -> List[pd.DatetimeIndex]:
        """计算水下期"""
        underwater_periods = []
        current_period = []

        for date, value in drawdown.items():
            if value < 0:
                current_period.append(date)
            elif current_period:
                underwater_periods.append(pd.DatetimeIndex(current_period))
                current_period = []

        if current_period:
            underwater_periods.append(pd.DatetimeIndex(current_period))

        return underwater_periods

    def _add_monthly_returns_heatmap(self, fig: go.Figure, row: int, col: int):
        """添加月度收益热图"""
        # 计算月度收益
        returns = self.portfolio.returns()
        monthly_returns = returns.resample(
            'M').apply(lambda x: (1 + x).prod() - 1)

        # 创建月度收益矩阵
        monthly_returns_matrix = monthly_returns.groupby(
            [monthly_returns.index.year, monthly_returns.index.month]).first().unstack()

        # 如果数据不足，填充缺失值
        if monthly_returns_matrix.empty:
            years = list(range(pd.to_datetime(self.start_date).year,
                         pd.to_datetime(self.end_date).year + 1))
            months = list(range(1, 13))
            monthly_returns_matrix = pd.DataFrame(index=years, columns=months)

        # 转换为numpy数组
        z = monthly_returns_matrix.values

        # 创建月份和年份标签
        month_labels = ['一月', '二月', '三月', '四月', '五月',
                        '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
        year_labels = [str(year) for year in monthly_returns_matrix.index]

        # 添加热图
        fig.add_trace(
            go.Heatmap(
                z=z * 100,  # 转换为百分比
                x=month_labels,
                y=year_labels,
                colorscale='RdYlGn',
                zmid=0,
                text=[[f"{val:.2%}" if not np.isnan(
                    val) else "" for val in row] for row in z],
                texttemplate="%{text}",
                colorbar=dict(title="收益率 (%)"),
                name='月度收益'
            ),
            row=row, col=col
        )

        # 更新轴标签
        fig.update_xaxes(title_text='月份', row=row, col=col)
        fig.update_yaxes(title_text='年份', row=row, col=col)

    def _add_trade_distribution(self, fig, row, col):
        """
        添加交易分布图
        """
        try:
            # 尝试从引擎获取交易记录
            trades = self._engine.get_all_trades()

            if not trades:
                self.logger.warning("没有交易记录，跳过生成交易分布图")
                # 在子图位置添加文本提示没有交易记录
                fig.add_annotation(
                    text="没有交易记录",
                    xref='x',  # 修改 xref
                    yref='y',  # 修改 yref
                    x=0.5,
                    y=0.5,
                    showarrow=False,
                    font=dict(size=16)
                )
                return

            # 将交易记录转换为 DataFrame
            trades_df = pd.DataFrame(trades)

            # 确保盈亏列是数值类型
            trades_df['profit_loss'] = pd.to_numeric(trades_df['profit_loss'])

            # 计算交易统计信息
            total_trades = len(trades_df)
            winning_trades = trades_df[trades_df['profit_loss'] > 0]
            losing_trades = trades_df[trades_df['profit_loss'] < 0]
            win_rate = len(winning_trades) / \
                total_trades if total_trades > 0 else 0
            avg_profit = winning_trades['profit_loss'].mean() if len(
                winning_trades) > 0 else 0
            avg_loss = losing_trades['profit_loss'].mean() if len(
                losing_trades) > 0 else 0
            profit_loss_ratio = abs(
                avg_profit / avg_loss) if avg_loss != 0 else np.inf

            # 创建交易分布散点图
            scatter = go.Scatter(
                x=trades_df['duration_days'],
                y=trades_df['profit_loss'],
                mode='markers',
                marker=dict(
                    size=8,
                    color=trades_df['profit_loss'],  # 颜色映射到盈亏
                    colorscale='RdYlGn',  # 红黄绿颜色映射
                    colorbar=dict(title='盈亏'),
                    line=dict(width=1, color='DarkSlateGrey')
                ),
                name='交易分布'
            )

            fig.add_trace(scatter, row=row, col=col)

            # 添加零线
            fig.add_shape(
                type="line",
                x0=trades_df['duration_days'].min(),
                y0=0,
                x1=trades_df['duration_days'].max(),
                y1=0,
                line=dict(
                    color="grey",
                    width=1,
                    dash="dash",
                ),
                row=row,
                col=col
            )

            # 添加交易统计注释
            annotations_text = f"总交易次数: {total_trades}<br>胜率: {win_rate:.2%}<br>平均盈利: {avg_profit:.2f}<br>平均亏损: {avg_loss:.2f}<br>盈亏比: {profit_loss_ratio:.2f}"
            fig.add_annotation(
                text=annotations_text,
                xref='x',  # 修改 xref
                yref='y',  # 修改 yref
                x=1,  # 放置在图的右侧
                y=1,  # 放置在图的顶部
                showarrow=False,
                bgcolor="rgba(255, 255, 255, 0.8)",  # 半透明背景
                align="left"
            )

            fig.update_layout(
                title_text='交易分布',  # 更新子图标题
                xaxis_title='持有天数',
                yaxis_title='盈亏'
            )

        except Exception as e:
            self.logger.error(f"生成交易分布图时出错: {e}")
            # 在子图位置添加错误提示
            fig.add_annotation(
                text=f"生成交易分布图时出错: {e}",
                xref='x',  # 修改 xref
                yref='y',  # 修改 yref
                x=0.5,
                y=0.5,
                showarrow=False,
                font=dict(color="red", size=12)
            )

    def _add_trade_details_table(self, fig, row, col):
        """
        添加交易详情表格
        """
        try:
            # 优先从引擎获取交易记录，其次从portfolio.trades获取
            trade_records = self._engine.get_all_trades() if hasattr(self, '_engine') and self._engine else []
            if not trade_records and hasattr(self.portfolio, 'trades'):
                trades = self.portfolio.trades
                if hasattr(trades, 'count') and trades.count() > 0:
                    trade_records = []
                    for i in range(trades.count()):
                        try:
                            trade_records.append({
                                'entry_time': getattr(trades.entry_idx[i], 'strftime', lambda x: '')('%Y-%m-%d %H:%M'),
                                'exit_time': getattr(trades.exit_idx[i], 'strftime', lambda x: '')('%Y-%m-%d %H:%M'),
                                'entry_price': float(getattr(trades, 'entry_price', [0])[i]),
                                'exit_price': float(getattr(trades, 'exit_price', [0])[i]),
                                'quantity': float(getattr(trades, 'size', [0])[i]),
                                'direction': 'buy' if float(getattr(trades, 'returns', [0])[i]) > 0 else 'sell',
                                'profit_loss': float(getattr(trades, 'pnl', [0])[i]),
                                'duration_days': getattr(getattr(trades, 'duration', [pd.Timedelta(0)])[i], 'days', 0)
                            })
                        except Exception as e:
                            self.logger.warning(f"转换第{i}笔交易记录时出错: {e}")
                            continue

            if not trade_records:
                self.logger.warning("没有交易记录，跳过添加交易详情表格")
                # 在子图位置添加文本提示没有交易记录
                fig.add_annotation(
                    text="没有交易记录",
                    xref='x',  # 修改 xref
                    yref='y',  # 修改 yref
                    x=0.5,
                    y=0.5,
                    showarrow=False,
                    font=dict(size=16)
                )
                return

            # 将交易记录转换为 DataFrame
            trades_df = pd.DataFrame(trade_records)

            # 格式化日期和数字
            trades_df['entry_time'] = pd.to_datetime(
                trades_df['entry_time']).dt.strftime('%Y-%m-%d %H:%M')
            trades_df['exit_time'] = pd.to_datetime(
                trades_df['exit_time']).dt.strftime('%Y-%m-%d %H:%M')
            trades_df['entry_price'] = trades_df['entry_price'].round(2)
            trades_df['exit_price'] = trades_df['exit_price'].round(2)
            trades_df['profit_loss'] = trades_df['profit_loss'].round(2)

            # 创建表格
            table = go.Table(
                header=dict(values=['入场时间', '出场时间', '入场价格', '出场价格', '数量', '方向', '盈亏', '持有天数'],
                            fill_color='lightblue',
                            align='left'),
                cells=dict(values=[trades_df['entry_time'], trades_df['exit_time'], trades_df['entry_price'],
                                   trades_df['exit_price'], trades_df['quantity'], trades_df['direction'],
                                   trades_df['profit_loss'], trades_df['duration_days']],
                           fill_color='lavender',
                           align='left')
            )

            fig.add_trace(table, row=row, col=col)

            fig.update_layout(
                title_text='交易详情'
            )

        except Exception as e:
            self.logger.error(f"添加交易详情表格时出错: {e}")
            # 在子图位置添加错误提示
            fig.add_annotation(
                text=f"添加交易详情表格时出错: {e}",
                xref='x',  # 修改 xref
                yref='y',  # 修改 yref
                x=0.5,
                y=0.5,
                showarrow=False,
                font=dict(color="red", size=12)
            )

    def _add_returns_distribution(self, fig: go.Figure, row: int, col: int):
        """添加收益分布"""
        # 计算日收益率
        returns = self.portfolio.returns()

        if len(returns) == 0:
            # 如果没有数据，添加空白图表
            fig.add_trace(
                go.Scatter(
                    x=[],
                    y=[],
                    mode='markers',
                    name='无收益数据'
                ),
                row=row, col=col
            )

            fig.add_annotation(
                xref="x domain",
                yref="y domain",
                x=0.5,
                y=0.5,
                text="无收益数据",
                showarrow=False,
                font=dict(size=20),
                row=row, col=col
            )

            return

        # 添加直方图
        fig.add_trace(
            go.Histogram(
                x=returns * 100,  # 转换为百分比
                name='收益分布',
                marker_color='blue',
                opacity=0.7,
                nbinsx=30,
                histnorm='probability'
            ),
            row=row, col=col
        )

        # 添加正态分布曲线
        x = np.linspace(returns.min() * 100, returns.max() * 100, 100)
        y = np.exp(-(x - returns.mean() * 100)**2 / (2 * (returns.std()
                   * 100)**2)) / (returns.std() * 100 * np.sqrt(2 * np.pi))

        fig.add_trace(
            go.Scatter(
                x=x,
                y=y,
                mode='lines',
                name='正态分布',
                line=dict(color='red', width=2)
            ),
            row=row, col=col
        )

        # 更新轴标签
        fig.update_xaxes(title_text='日收益率 (%)', row=row, col=col)
        fig.update_yaxes(title_text='概率密度', row=row, col=col)

        # 添加统计注释
        skew = returns.skew()
        kurt = returns.kurtosis()

        annotations = [
            f"平均日收益率: {returns.mean():.2%}",
            f"日收益率标准差: {returns.std():.2%}",
            f"偏度: {skew:.2f}",
            f"峰度: {kurt:.2f}",
            f"正收益天数: {(returns > 0).sum()} ({(returns > 0).mean():.2%})",
            f"负收益天数: {(returns < 0).sum()} ({(returns < 0).mean():.2%})"
        ]

        annotation_text = "<br>".join(annotations)

        fig.add_annotation(
            xref="x domain",
            yref="y domain",
            x=0.01,
            y=0.99,
            text=annotation_text,
            showarrow=False,
            font=dict(size=12),
            align="left",
            bgcolor="rgba(255, 255, 255, 0.8)",
            bordercolor="black",
            borderwidth=1,
            borderpad=4,
            row=row, col=col
        )

    def _add_strategy_params_table(self, fig: go.Figure, row: int, col: int):
        """添加策略参数表格"""
        try:
            # 准备表格数据，处理可能缺失的属性
            fees = getattr(self.portfolio, 'fees', 0.0)
            slippage = getattr(self.portfolio, 'slippage', 0.0)
            size = getattr(self.portfolio, 'size', '动态')
            
            params_data = pd.DataFrame({
                '参数': list(self.params.keys()) + ['初始资金', '手续费率', '滑点率', '仓位比例'],
                '值': list(self.params.values()) + [
                    f"{self.portfolio.init_cash:.2f}",
                    f"{fees:.2%}",
                    f"{slippage:.2%}",
                    f"{size if isinstance(size, (int, float)) else '动态'}"
                ]
            })
        except Exception as e:
            self.logger.warning(f"生成策略参数表格时出错: {e}")
            # 提供默认参数表格
            params_data = pd.DataFrame({
                '参数': list(self.params.keys()) + ['初始资金'],
                '值': list(self.params.values()) + [f"{self.portfolio.init_cash:.2f}"]
            })

        # 添加表格
        fig.add_trace(
            go.Table(
                header=dict(
                    values=['参数', '值'],
                    align='center',
                    font=dict(size=12, color='white'),
                    fill_color='darkblue'
                ),
                cells=dict(
                    values=[params_data['参数'], params_data['值']],
                    align=['left', 'right'],
                    font=dict(size=11),
                    height=25
                )
            ),
            row=row, col=col
        )

        # 添加性能指标表格前先验证数据
        self.logger.info(f"Metrics数据验证: {self.metrics}")
        
        # 确保所有指标都有默认值
        default_metrics = {
            'total_return': 0,
            'annual_return': 0,
            'sharpe_ratio': 0,
            'sortino_ratio': 0,
            'calmar_ratio': 0,
            'max_drawdown': 0,
            'win_rate': 0,
            'profit_factor': 0,
            'trade_count': 0,
            'avg_trade_duration': 0
        }
        self.metrics = {**default_metrics, **self.metrics}
        
        metrics_data = pd.DataFrame({
            '指标': [
                '总收益率', '年化收益率', '夏普比率', '索提诺比率', '卡尔玛比率',
                '最大回撤', '胜率', '盈亏比', '交易次数', '平均持仓天数'
            ],
            '值': [
                f"{self.metrics['total_return']:.2%}",
                f"{self.metrics['annual_return']:.2%}",
                f"{self.metrics['sharpe_ratio']:.2f}",
                f"{self.metrics['sortino_ratio']:.2f}",
                f"{self.metrics['calmar_ratio']:.2f}" if not np.isnan(self.metrics['calmar_ratio']) else 'N/A',
                f"{self.metrics['max_drawdown']:.2%}",
                f"{self.metrics['win_rate']:.2%}",
                f"{self.metrics['profit_factor']:.2f}",
                f"{self.metrics['trade_count']}",
                f"{self.metrics['avg_trade_duration']:.1f}" if not np.isnan(self.metrics['avg_trade_duration']) else 'N/A'
            ]
        })

        # 添加性能指标表格到图表
        fig.add_trace(
            go.Table(
                header=dict(
                    values=list(metrics_data.columns),
                    fill_color='paleturquoise',
                    align='left'
                ),
                cells=dict(
                    values=[metrics_data[col] for col in metrics_data.columns],
                    fill_color='lavender',
                    align='left'
                )
            ),
            row=3, col=1
        )

        # 添加第二个表格
        fig.add_trace(
            go.Table(
                header=dict(
                    values=['指标', '值'],
                    align='center',
                    font=dict(size=12, color='white'),
                    fill_color='darkgreen'
                ),
                cells=dict(
                    values=[metrics_data['指标'], metrics_data['值']],
                    align=['left', 'right'],
                    font=dict(size=11),
                    height=25
                ),
                domain=dict(x=[0, 1], y=[0, 0.45])  # 放在第一个表格下方
            ),
            row=row, col=col
        )

    def generate_json_report(self, filename: Optional[str] = None) -> str:
        """
        生成JSON报告

        参数:
            filename (str): 文件名，如果为None则自动生成

        返回:
            str: 报告文件路径
        """
        # 生成文件名
        if filename is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            symbol_clean = self.symbol.replace('/', '_').replace(':', '_')
            filename = f"{self.strategy_name.lower()}_{symbol_clean}_{timestamp}.json"

        # 完整文件路径
        file_path = self.output_dir / filename

        # 准备报告数据
        report_data = {
            "strategy": self.strategy_name,
            "symbol": self.symbol,
            "period": {
                "start": self.start_date,
                "end": self.end_date
            },
            "parameters": self.params,
            "metrics": self.metrics,
            "trades_summary": {
                "count": int(getattr(getattr(self.portfolio, 'trades', None), 'count', lambda: 0)()),
                "win_rate": float(getattr(getattr(self.portfolio, 'trades', None), 'win_rate', lambda: 0.0)()),
                "profit_factor": float(getattr(getattr(self.portfolio, 'trades', None), 'profit_factor', lambda: 0.0)()),
                "avg_trade_duration": float(getattr(getattr(getattr(self.portfolio, 'trades', None), 'duration', pd.Series([])), 'mean', lambda: pd.Timedelta(0))().total_seconds() / 86400) if getattr(getattr(self.portfolio, 'trades', None), 'count', lambda: 0)() > 0 else None
            }
        }

        # 添加交易详情
        trades = getattr(self.portfolio, 'trades', None)
        if trades and hasattr(trades, 'count') and trades.count() > 0:
            trades_data = []
            for i in range(trades.count()):
                try:
                    trade = {
                        "entry_time": getattr(trades.entry_idx[i], 'strftime', lambda x: '')('%Y-%m-%d'),
                        "exit_time": getattr(trades.exit_idx[i], 'strftime', lambda x: '')('%Y-%m-%d'),
                        "entry_price": float(getattr(trades, 'entry_price', [0])[i]),
                        "exit_price": float(getattr(trades, 'exit_price', [0])[i]),
                        "return": float(getattr(trades, 'returns', [0])[i]),
                        "pnl": float(getattr(trades, 'pnl', [0])[i]),
                        "duration_days": getattr(getattr(trades, 'duration', [pd.Timedelta(0)])[i], 'days', 0)
                    }
                    trades_data.append(trade)
                except Exception as e:
                    self.logger.warning(f"处理第{i}笔交易记录时出错: {e}")
                    continue

            report_data["trades"] = trades_data

        # 保存为JSON文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=4)

        return str(file_path)

    def _add_rolling_metrics(self, fig, row, col):
        """
        添加滚动窗口指标图表
        Args:
            fig: plotly Figure对象
            row: 子图行位置
            col: 子图列位置
        """
        try:
            # 计算滚动收益率和夏普比率
            returns = self.portfolio.returns()
            rolling_returns = returns.rolling(30).mean()
            rolling_sharpe = returns.rolling(30).mean() / returns.rolling(30).std() * np.sqrt(252)

            # 添加secondary y轴
            fig.update_layout({
                f'yaxis{2*(row-1)+col}': dict(
                    title='收益率',
                    side='left',
                    showgrid=False
                ),
                f'yaxis{2*(row-1)+col+1}': dict(
                    title='夏普比率',
                    side='right',
                    overlaying=f'y{2*(row-1)+col}',
                    showgrid=False
                )
            })

            # 添加滚动收益率(主坐标轴)
            fig.add_trace(
                go.Scatter(
                    x=rolling_returns.index,
                    y=rolling_returns,
                    name='30日滚动收益率',
                    mode='lines',
                    line=dict(width=2, color='#FF7F0E')
                ),
                row=row, col=col
            )

            # 添加滚动夏普比率(次坐标轴)
            fig.add_trace(
                go.Scatter(
                    x=rolling_sharpe.index,
                    y=rolling_sharpe,
                    name='30日滚动夏普比率',
                    mode='lines',
                    line=dict(width=2, color='#1F77B4'),
                    yaxis=f'y{2*(row-1)+col+1}'
                ),
                row=row, col=col
            )

        except Exception as e:
            self.logger.error(f"添加滚动指标时出错: {e}")
            fig.add_annotation(
                xref="x domain",
                yref="y domain",
                x=0.5,
                y=0.5,
                text=f"无法生成滚动指标: {str(e)}",
                showarrow=False,
                font=dict(color="red", size=12),
                row=row, col=col
            )

    def _create_trade_distribution(self):
        """
        生成交易分布图表（修复列重复问题）
        """
        # 修复前：可能重复添加同名列
        trades = self.backtest_data.trades
        if not trades.empty:
            # 使用原始列名判断
            if 'entry_time' in trades.columns:
                trades = trades.assign(
                    hour=trades['entry_time'].dt.hour
                ).rename(columns={'hour': 'trade_hour'})