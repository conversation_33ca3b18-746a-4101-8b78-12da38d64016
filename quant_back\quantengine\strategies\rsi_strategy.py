import pandas as pd
import numpy as np
import vectorbt as vbt
from .base_strategy import BaseStrategy

class RsiStrategy(BaseStrategy):
    """相对强弱指数(RSI)策略"""
    
    def __init__(self, params=None):
        super().__init__(params)
        self.window = self.params.get('window', 14)
        self.overbought = self.params.get('overbought', 70)
        self.oversold = self.params.get('oversold', 30)
    
    @staticmethod
    def get_params_schema():
        return {
            "window": {
                "type": "integer",
                "title": "RSI周期",
                "default": 14,
                "minimum": 2,
                "maximum": 50
            },
            "overbought": {
                "type": "integer",
                "title": "超买阈值",
                "default": 70,
                "minimum": 50,
                "maximum": 100
            },
            "oversold": {
                "type": "integer",
                "title": "超卖阈值",
                "default": 30,
                "minimum": 0,
                "maximum": 50
            }
        }
    
    def run(self, data):
        # 计算RSI指标
        rsi = vbt.RSI.run(data['close'], window=self.window).rsi
        
        # 生成信号
        entries = (rsi < self.oversold).astype('bool')  # 超卖买入
        exits = (rsi > self.overbought).astype('bool')  # 超买卖出
        
        return entries, exits