import pandas as pd
import numpy as np
from typing import Union, Optional, Tuple, List, Dict
import talib
from scipy import stats

class EnhancedIndicators:
    """增强版技术指标计算类"""

    @staticmethod
    def sma(data: pd.Series, period: int) -> pd.Series:
        """
        简单移动平均线

        参数:
            data (pd.Series): 价格序列
            period (int): 周期

        返回:
            pd.Series: 移动平均线
        """
        return talib.SMA(data, timeperiod=period)

    @staticmethod
    def ema(data: pd.Series, period: int) -> pd.Series:
        """
        指数移动平均线

        参数:
            data (pd.Series): 价格序列
            period (int): 周期

        返回:
            pd.Series: 指数移动平均线
        """
        return talib.EMA(data, timeperiod=period)

    @staticmethod
    def wma(data: pd.Series, period: int) -> pd.Series:
        """
        加权移动平均线

        参数:
            data (pd.Series): 价格序列
            period (int): 周期

        返回:
            pd.Series: 加权移动平均线
        """
        return talib.WMA(data, timeperiod=period)

    @staticmethod
    def kama(data: pd.Series, period: int) -> pd.Series:
        """
        考夫曼自适应移动平均线

        参数:
            data (pd.Series): 价格序列
            period (int): 周期

        返回:
            pd.Series: 考夫曼自适应移动平均线
        """
        return talib.KAMA(data, timeperiod=period)

    @staticmethod
    def macd(data: pd.Series, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        MACD指标

        参数:
            data (pd.Series): 价格序列
            fast_period (int): 快线周期
            slow_period (int): 慢线周期
            signal_period (int): 信号线周期

        返回:
            Tuple[pd.Series, pd.Series, pd.Series]: MACD线, 信号线, 柱状图
        """
        macd, signal, hist = talib.MACD(
            data,
            fastperiod=fast_period,
            slowperiod=slow_period,
            signalperiod=signal_period
        )
        return macd, signal, hist

    @staticmethod
    def rsi(data: pd.Series, period: int = 14) -> pd.Series:
        """
        相对强弱指标

        参数:
            data (pd.Series): 价格序列
            period (int): 周期

        返回:
            pd.Series: RSI值
        """
        return talib.RSI(data, timeperiod=period)

    @staticmethod
    def stoch(high: pd.Series, low: pd.Series, close: pd.Series,
              k_period: int = 14, d_period: int = 3, slowing: int = 3) -> Tuple[pd.Series, pd.Series]:
        """
        随机指标

        参数:
            high (pd.Series): 最高价序列
            low (pd.Series): 最低价序列
            close (pd.Series): 收盘价序列
            k_period (int): K线周期
            d_period (int): D线周期
            slowing (int): 慢化周期

        返回:
            Tuple[pd.Series, pd.Series]: K线, D线
        """
        k, d = talib.STOCH(
            high, low, close,
            fastk_period=k_period,
            slowk_period=slowing,
            slowk_matype=0,
            slowd_period=d_period,
            slowd_matype=0
        )
        return k, d

    @staticmethod
    def bollinger_bands(data: pd.Series, period: int = 20, std_dev: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        布林带

        参数:
            data (pd.Series): 价格序列
            period (int): 周期
            std_dev (float): 标准差倍数

        返回:
            Tuple[pd.Series, pd.Series, pd.Series]: 上轨, 中轨, 下轨
        """
        upper, middle, lower = talib.BBANDS(
            data,
            timeperiod=period,
            nbdevup=std_dev,
            nbdevdn=std_dev,
            matype=0
        )
        return upper, middle, lower

    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """
        平均真实范围

        参数:
            high (pd.Series): 最高价序列
            low (pd.Series): 最低价序列
            close (pd.Series): 收盘价序列
            period (int): 周期

        返回:
            pd.Series: ATR值
        """
        return talib.ATR(high, low, close, timeperiod=period)

    @staticmethod
    def adx(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """
        平均方向指数

        参数:
            high (pd.Series): 最高价序列
            low (pd.Series): 最低价序列
            close (pd.Series): 收盘价序列
            period (int): 周期

        返回:
            pd.Series: ADX值
        """
        return talib.ADX(high, low, close, timeperiod=period)

    @staticmethod
    def cci(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """
        商品通道指数

        参数:
            high (pd.Series): 最高价序列
            low (pd.Series): 最低价序列
            close (pd.Series): 收盘价序列
            period (int): 周期

        返回:
            pd.Series: CCI值
        """
        return talib.CCI(high, low, close, timeperiod=period)

    @staticmethod
    def obv(close: pd.Series, volume: pd.Series) -> pd.Series:
        """
        能量潮

        参数:
            close (pd.Series): 收盘价序列
            volume (pd.Series): 成交量序列

        返回:
            pd.Series: OBV值
        """
        return talib.OBV(close, volume)

    @staticmethod
    def roc(data: pd.Series, period: int = 10) -> pd.Series:
        """
        变动率

        参数:
            data (pd.Series): 价格序列
            period (int): 周期

        返回:
            pd.Series: ROC值
        """
        return talib.ROC(data, timeperiod=period)

    @staticmethod
    def williams_r(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """
        威廉指标

        参数:
            high (pd.Series): 最高价序列
            low (pd.Series): 最低价序列
            close (pd.Series): 收盘价序列
            period (int): 周期

        返回:
            pd.Series: 威廉指标值
        """
        return talib.WILLR(high, low, close, timeperiod=period)

    @staticmethod
    def ichimoku(high: pd.Series, low: pd.Series,
                conversion_period: int = 9, base_period: int = 26,
                span_b_period: int = 52, displacement: int = 26) -> Dict[str, pd.Series]:
        """
        一目均衡表

        参数:
            high (pd.Series): 最高价序列
            low (pd.Series): 最低价序列
            conversion_period (int): 转换线周期
            base_period (int): 基准线周期
            span_b_period (int): B跨度周期
            displacement (int): 位移周期

        返回:
            Dict[str, pd.Series]: 一目均衡表各线
        """
        # 转换线 (Conversion Line)
        conversion = pd.Series((high.rolling(window=conversion_period).max() +
                               low.rolling(window=conversion_period).min()) / 2,
                              index=high.index)

        # 基准线 (Base Line)
        base = pd.Series((high.rolling(window=base_period).max() +
                         low.rolling(window=base_period).min()) / 2,
                        index=high.index)

        # 先行跨度A (Leading Span A)
        span_a = pd.Series((conversion + base) / 2, index=high.index).shift(displacement)

        # 先行跨度B (Leading Span B)
        span_b = pd.Series((high.rolling(window=span_b_period).max() +
                           low.rolling(window=span_b_period).min()) / 2,
                          index=high.index).shift(displacement)

        # 延迟线 (Lagging Span)
        lagging = pd.Series(high.shift(-displacement), index=high.index)

        return {
            'conversion': conversion,
            'base': base,
            'span_a': span_a,
            'span_b': span_b,
            'lagging': lagging
        }

    @staticmethod
    def zigzag(data: pd.Series, deviation: float = 0.05) -> pd.Series:
        """
        ZigZag指标

        参数:
            data (pd.Series): 价格序列
            deviation (float): 偏差阈值

        返回:
            pd.Series: ZigZag值
        """
        zigzag = pd.Series(index=data.index, dtype=float)

        # 初始化变量
        last_peak = data.iloc[0]
        last_peak_idx = data.index[0]
        last_trough = data.iloc[0]
        last_trough_idx = data.index[0]
        trend = 0  # 0: 未确定, 1: 上升, -1: 下降

        for i, (idx, price) in enumerate(data.items()):
            if i == 0:
                continue

            if trend == 0:
                # 初始趋势未确定
                if price > last_peak:
                    last_peak = price
                    last_peak_idx = idx
                    trend = 1
                elif price < last_trough:
                    last_trough = price
                    last_trough_idx = idx
                    trend = -1
            elif trend == 1:
                # 上升趋势
                if price > last_peak:
                    # 新高点
                    last_peak = price
                    last_peak_idx = idx
                elif price < last_peak * (1 - deviation):
                    # 下跌超过阈值，趋势反转
                    zigzag[last_peak_idx] = last_peak
                    last_trough = price
                    last_trough_idx = idx
                    trend = -1
            elif trend == -1:
                # 下降趋势
                if price < last_trough:
                    # 新低点
                    last_trough = price
                    last_trough_idx = idx
                elif price > last_trough * (1 + deviation):
                    # 上涨超过阈值，趋势反转
                    zigzag[last_trough_idx] = last_trough
                    last_peak = price
                    last_peak_idx = idx
                    trend = 1

        # 添加最后一个点
        if trend == 1:
            zigzag[last_peak_idx] = last_peak
        elif trend == -1:
            zigzag[last_trough_idx] = last_trough

        return zigzag

    @staticmethod
    def hurst_exponent(data: pd.Series, min_lag: int = 2, max_lag: int = 20) -> float:
        """
        计算赫斯特指数

        参数:
            data (pd.Series): 价格序列
            min_lag (int): 最小滞后期
            max_lag (int): 最大滞后期

        返回:
            float: 赫斯特指数
        """
        # 计算对数收益率
        returns = np.log(data / data.shift(1)).dropna()

        # 计算不同滞后期的重标准化范围
        lags = range(min_lag, max_lag)
        tau = [np.sqrt(np.std(np.subtract(returns[lag:], returns[:-lag]))) for lag in lags]

        # 使用对数回归计算赫斯特指数
        m = np.polyfit(np.log(lags), np.log(tau), 1)
        hurst = m[0] / 2.0

        return hurst

    @staticmethod
    def fractal_dimension(data: pd.Series, window: int = 10) -> pd.Series:
        """
        计算分形维度

        参数:
            data (pd.Series): 价格序列
            window (int): 窗口大小

        返回:
            pd.Series: 分形维度序列
        """
        fd = pd.Series(index=data.index)

        for i in range(window, len(data)):
            # 获取窗口数据
            window_data = data.iloc[i-window:i]

            # 计算最大值和最小值
            max_val = window_data.max()
            min_val = window_data.min()

            if max_val == min_val:
                fd.iloc[i] = 1.0
                continue

            # 归一化数据
            normalized = (window_data - min_val) / (max_val - min_val)

            # 计算盒维数
            n = 10  # 盒子数量
            box_sizes = np.logspace(-1, 0, n)
            counts = np.zeros(n)

            for j, eps in enumerate(box_sizes):
                # 计算覆盖所需的盒子数量
                count = 0
                covered = np.zeros(len(normalized))

                for k in range(len(normalized)):
                    if covered[k]:
                        continue

                    count += 1
                    covered[k] = 1

                    # 标记所有在eps范围内的点
                    for l in range(k+1, len(normalized)):
                        if abs(normalized.iloc[l] - normalized.iloc[k]) < eps:
                            covered[l] = 1

                counts[j] = count

            # 使用对数回归计算分形维度
            x = np.log(1 / box_sizes)
            y = np.log(counts)
            m = np.polyfit(x, y, 1)
            fd.iloc[i] = m[0]

        return fd

    @staticmethod
    def volatility(data: pd.Series, window: int = 20, annualization: int = 252) -> pd.Series:
        """
        计算波动率

        参数:
            data (pd.Series): 价格序列
            window (int): 窗口大小
            annualization (int): 年化因子

        返回:
            pd.Series: 波动率序列
        """
        # 计算对数收益率
        returns = np.log(data / data.shift(1)).dropna()

        # 计算滚动波动率
        vol = returns.rolling(window=window).std() * np.sqrt(annualization)

        return vol

    @staticmethod
    def beta(returns: pd.Series, market_returns: pd.Series, window: int = 252) -> pd.Series:
        """
        计算贝塔系数

        参数:
            returns (pd.Series): 收益率序列
            market_returns (pd.Series): 市场收益率序列
            window (int): 窗口大小

        返回:
            pd.Series: 贝塔系数序列
        """
        # 对齐数据
        aligned_returns = pd.DataFrame({'asset': returns, 'market': market_returns})
        aligned_returns = aligned_returns.dropna()

        # 计算滚动贝塔
        beta = aligned_returns['asset'].rolling(window=window).cov(aligned_returns['market']) / \
               aligned_returns['market'].rolling(window=window).var()

        return beta

    @staticmethod
    def sharpe_ratio(returns: pd.Series, risk_free_rate: float = 0.0, window: int = 252) -> pd.Series:
        """
        计算夏普比率

        参数:
            returns (pd.Series): 收益率序列
            risk_free_rate (float): 无风险利率
            window (int): 窗口大小

        返回:
            pd.Series: 夏普比率序列
        """
        excess_returns = returns - risk_free_rate / 252  # 假设日度数据

        # 计算滚动夏普比率
        sharpe = excess_returns.rolling(window=window).mean() / \
                 excess_returns.rolling(window=window).std() * np.sqrt(252)

        return sharpe

    @staticmethod
    def sortino_ratio(returns: pd.Series, risk_free_rate: float = 0.0, window: int = 252) -> pd.Series:
        """
        计算索提诺比率

        参数:
            returns (pd.Series): 收益率序列
            risk_free_rate (float): 无风险利率
            window (int): 窗口大小

        返回:
            pd.Series: 索提诺比率序列
        """
        excess_returns = returns - risk_free_rate / 252  # 假设日度数据

        # 计算下行标准差
        def downside_std(x):
            return np.sqrt(np.mean(np.minimum(x, 0)**2))

        # 计算滚动索提诺比率
        sortino = pd.Series(index=returns.index)
        for i in range(window, len(returns) + 1):
            window_returns = excess_returns.iloc[i-window:i]
            downside_risk = downside_std(window_returns)
            if downside_risk != 0:
                sortino.iloc[i-1] = window_returns.mean() / downside_risk * np.sqrt(252)
            else:
                sortino.iloc[i-1] = np.nan

        return sortino

    @staticmethod
    def maximum_drawdown(data: pd.Series, window: int = None) -> Union[float, pd.Series]:
        """
        计算最大回撤

        参数:
            data (pd.Series): 价格或资产净值序列
            window (int): 窗口大小，如果为None则计算整个序列的最大回撤

        返回:
            Union[float, pd.Series]: 最大回撤值或序列
        """
        if window is None:
            # 计算整个序列的最大回撤
            rolling_max = data.cummax()
            drawdown = (data / rolling_max - 1)
            return drawdown.min()
        else:
            # 计算滚动最大回撤
            rolling_max = data.rolling(window=window).max()
            drawdown = (data / rolling_max - 1)
            return drawdown

    @staticmethod
    def support_resistance(data: pd.Series, window: int = 20, threshold: float = 0.02) -> Dict[str, List[float]]:
        """
        计算支撑位和阻力位

        参数:
            data (pd.Series): 价格序列
            window (int): 窗口大小
            threshold (float): 价格聚集阈值

        返回:
            Dict[str, List[float]]: 支撑位和阻力位
        """
        # 使用zigzag找到关键点
        zigzag = EnhancedIndicators.zigzag(data, deviation=threshold)

        # 提取峰和谷
        peaks = zigzag[zigzag > 0]
        troughs = zigzag[zigzag < 0]

        # 计算价格区间
        price_range = data.max() - data.min()
        bin_size = price_range / 20  # 将价格范围分成20个区间

        # 创建价格直方图
        hist, bin_edges = np.histogram(data, bins=np.arange(data.min(), data.max() + bin_size, bin_size))

        # 找到价格聚集区域
        support_levels = []
        resistance_levels = []

        for i in range(len(hist)):
            if hist[i] > np.mean(hist) + np.std(hist):
                price_level = (bin_edges[i] + bin_edges[i+1]) / 2

                # 判断是支撑位还是阻力位
                if price_level < data.iloc[-1]:
                    support_levels.append(price_level)
                else:
                    resistance_levels.append(price_level)

        # 添加最近的峰和谷
        recent_peaks = peaks[-3:] if len(peaks) >= 3 else peaks
        recent_troughs = troughs[-3:] if len(troughs) >= 3 else troughs

        for peak in recent_peaks:
            if peak not in resistance_levels:
                resistance_levels.append(peak)

        for trough in recent_troughs:
            if trough not in support_levels:
                support_levels.append(trough)

        return {
            'support': sorted(support_levels),
            'resistance': sorted(resistance_levels)
        }

    @staticmethod
    def fibonacci_levels(high: float, low: float) -> Dict[str, float]:
        """
        计算斐波那契回调水平

        参数:
            high (float): 最高价
            low (float): 最低价

        返回:
            Dict[str, float]: 斐波那契水平
        """
        diff = high - low

        return {
            '0.0': low,
            '0.236': low + 0.236 * diff,
            '0.382': low + 0.382 * diff,
            '0.5': low + 0.5 * diff,
            '0.618': low + 0.618 * diff,
            '0.786': low + 0.786 * diff,
            '1.0': high
        }

    @staticmethod
    def pivot_points(high: float, low: float, close: float) -> Dict[str, float]:
        """
        计算枢轴点

        参数:
            high (float): 最高价
            low (float): 最低价
            close (float): 收盘价

        返回:
            Dict[str, float]: 枢轴点水平
        """
        pivot = (high + low + close) / 3

        s1 = 2 * pivot - high
        s2 = pivot - (high - low)
        s3 = low - 2 * (high - pivot)

        r1 = 2 * pivot - low
        r2 = pivot + (high - low)
        r3 = high + 2 * (pivot - low)

        return {
            'pivot': pivot,
            'r1': r1,
            'r2': r2,
            'r3': r3,
            's1': s1,
            's2': s2,
            's3': s3
        }

    @staticmethod
    def linear_regression(data: pd.Series, window: int = 20) -> Dict[str, pd.Series]:
        """
        计算线性回归

        参数:
            data (pd.Series): 价格序列
            window (int): 窗口大小

        返回:
            Dict[str, pd.Series]: 线性回归结果
        """
        result = {}

        # 计算滚动线性回归
        result['slope'] = pd.Series(index=data.index)
        result['intercept'] = pd.Series(index=data.index)
        result['r_value'] = pd.Series(index=data.index)
        result['p_value'] = pd.Series(index=data.index)
        result['std_err'] = pd.Series(index=data.index)
        result['predicted'] = pd.Series(index=data.index)

        for i in range(window, len(data) + 1):
            y = data.iloc[i-window:i].values
            x = np.arange(window)

            slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)

            result['slope'].iloc[i-1] = slope
            result['intercept'].iloc[i-1] = intercept
            result['r_value'].iloc[i-1] = r_value
            result['p_value'].iloc[i-1] = p_value
            result['std_err'].iloc[i-1] = std_err
            result['predicted'].iloc[i-1] = intercept + slope * (window - 1)

        # 计算上下通道
        result['upper'] = result['predicted'] + 2 * data.rolling(window=window).std()
        result['lower'] = result['predicted'] - 2 * data.rolling(window=window).std()

        return result

    @staticmethod
    def vwap(data: pd.DataFrame, volume_column: str = 'volume') -> pd.Series:
        """
        计算成交量加权平均价格

        参数:
            data (pd.DataFrame): 价格和成交量数据
            volume_column (str): 成交量列名

        返回:
            pd.Series: VWAP值
        """
        typical_price = (data['high'] + data['low'] + data['close']) / 3
        vwap = (typical_price * data[volume_column]).cumsum() / data[volume_column].cumsum()
        return vwap

    @staticmethod
    def money_flow_index(high: pd.Series, low: pd.Series, close: pd.Series,
                         volume: pd.Series, period: int = 14) -> pd.Series:
        """
        计算资金流量指标

        参数:
            high (pd.Series): 最高价序列
            low (pd.Series): 最低价序列
            close (pd.Series): 收盘价序列
            volume (pd.Series): 成交量序列
            period (int): 周期

        返回:
            pd.Series: MFI值
        """
        typical_price = (high + low + close) / 3
        raw_money_flow = typical_price * volume

        # 计算正向和负向资金流
        price_change = typical_price.diff()
        positive_flow = pd.Series(0, index=typical_price.index)
        negative_flow = pd.Series(0, index=typical_price.index)

        positive_flow[price_change > 0] = raw_money_flow[price_change > 0]
        negative_flow[price_change < 0] = raw_money_flow[price_change < 0]

        # 计算资金比率
        positive_mf = positive_flow.rolling(window=period).sum()
        negative_mf = negative_flow.rolling(window=period).sum()

        money_ratio = positive_mf / negative_mf

        # 计算MFI
        mfi = 100 - (100 / (1 + money_ratio))

        return mfi

    @staticmethod
    def keltner_channel(high: pd.Series, low: pd.Series, close: pd.Series,
                        period: int = 20, atr_period: int = 10, multiplier: float = 2.0) -> Dict[str, pd.Series]:
        """
        计算肯特纳通道

        参数:
            high (pd.Series): 最高价序列
            low (pd.Series): 最低价序列
            close (pd.Series): 收盘价序列
            period (int): EMA周期
            atr_period (int): ATR周期
            multiplier (float): ATR乘数

        返回:
            Dict[str, pd.Series]: 肯特纳通道
        """
        # 计算中线（EMA）
        middle = talib.EMA(close, timeperiod=period)

        # 计算ATR
        atr = talib.ATR(high, low, close, timeperiod=atr_period)

        # 计算上下轨
        upper = middle + multiplier * atr
        lower = middle - multiplier * atr

        return {
            'upper': upper,
            'middle': middle,
            'lower': lower
        }

    @staticmethod
    def donchian_channel(high: pd.Series, low: pd.Series, period: int = 20) -> Dict[str, pd.Series]:
        """
        计算唐奇安通道

        参数:
            high (pd.Series): 最