2025-05-21 17:45:27 - quantengine - INFO - 数据源加载完成
2025-05-21 17:45:27 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 17:45:27 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 17:45:27 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 17:45:27 - quantengine - INFO - 开始执行回测...
2025-05-21 17:45:27 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 17:45:28 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 17:45:28 - quantengine - INFO - 开始执行策略计算...
2025-05-21 17:45:29 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 17:45:29 - quantengine - INFO - 开始执行回测计算...
2025-05-21 17:45:35 - quantengine - INFO - 回测完成，最终资产:0.16
2025-05-21 17:45:35 - quantengine - INFO - 回测执行完成
2025-05-21 17:45:35 - quantengine - INFO - 开始生成报告...
2025-05-21 17:49:09 - quantengine - INFO - 数据源加载完成
2025-05-21 17:49:09 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 17:49:09 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 17:49:09 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 17:49:09 - quantengine - INFO - 开始执行回测...
2025-05-21 17:49:09 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 17:49:09 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 17:49:09 - quantengine - INFO - 开始执行策略计算...
2025-05-21 17:49:10 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 17:49:10 - quantengine - INFO - 开始执行回测计算...
2025-05-21 17:49:15 - quantengine - INFO - 回测完成，最终资产:0.75
2025-05-21 17:49:15 - quantengine - INFO - 回测执行完成
2025-05-21 17:49:15 - quantengine - INFO - 开始生成报告...
2025-05-21 17:53:24 - quantengine - INFO - 数据源加载完成
2025-05-21 17:53:24 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 17:53:24 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 17:53:24 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 17:53:24 - quantengine - INFO - 开始执行回测...
2025-05-21 17:53:24 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 17:53:24 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 17:53:24 - quantengine - INFO - 开始执行策略计算...
2025-05-21 17:53:25 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 17:53:25 - quantengine - INFO - 开始执行回测计算...
2025-05-21 17:53:30 - quantengine - INFO - 回测完成，最终资产:0.75
2025-05-21 17:53:30 - quantengine - INFO - 回测执行完成
2025-05-21 17:53:30 - quantengine - INFO - 开始生成报告...
2025-05-21 18:00:56 - quantengine - INFO - 数据源加载完成
2025-05-21 18:00:56 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 18:00:56 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 18:00:56 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 18:00:56 - quantengine - INFO - 开始执行回测...
2025-05-21 18:00:56 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 18:00:56 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 18:00:56 - quantengine - INFO - 开始执行策略计算...
2025-05-21 18:00:57 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 18:00:57 - quantengine - INFO - 开始执行回测计算...
2025-05-21 18:01:03 - quantengine - INFO - 回测完成，最终资产:0.75
2025-05-21 18:01:03 - quantengine - INFO - 回测执行完成
2025-05-21 18:01:03 - quantengine - INFO - 开始生成报告...
2025-05-21 18:06:20 - quantengine - INFO - 数据源加载完成
2025-05-21 18:06:20 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 18:06:20 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 18:06:20 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 18:06:20 - quantengine - INFO - 开始执行回测...
2025-05-21 18:06:20 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 18:06:20 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 18:06:20 - quantengine - INFO - 开始执行策略计算...
2025-05-21 18:06:21 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 18:06:21 - quantengine - INFO - 开始执行回测计算...
2025-05-21 18:06:28 - quantengine - INFO - 回测完成，最终资产:0.75
2025-05-21 18:06:28 - quantengine - INFO - 回测执行完成
2025-05-21 18:06:28 - quantengine - INFO - 开始生成报告...
2025-05-21 18:09:59 - quantengine - INFO - 数据源加载完成
2025-05-21 18:09:59 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 18:09:59 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 18:09:59 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 18:09:59 - quantengine - INFO - 开始执行回测...
2025-05-21 18:09:59 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 18:09:59 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 18:09:59 - quantengine - INFO - 开始执行策略计算...
2025-05-21 18:10:00 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 18:10:00 - quantengine - INFO - 开始执行回测计算...
2025-05-21 18:10:05 - quantengine - INFO - 回测完成，最终资产:0.75
2025-05-21 18:10:05 - quantengine - INFO - 回测执行完成
2025-05-21 18:10:05 - quantengine - INFO - 开始生成报告...
2025-05-21 18:11:33 - quantengine - INFO - 数据源加载完成
2025-05-21 18:11:33 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 18:11:33 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 18:11:33 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 18:11:33 - quantengine - INFO - 开始执行回测...
2025-05-21 18:11:33 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 18:11:34 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 18:11:34 - quantengine - INFO - 开始执行策略计算...
2025-05-21 18:11:35 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 18:11:35 - quantengine - INFO - 开始执行回测计算...
2025-05-21 18:11:40 - quantengine - INFO - 回测完成，最终资产:0.75
2025-05-21 18:11:40 - quantengine - INFO - 回测执行完成
2025-05-21 18:11:40 - quantengine - INFO - 开始生成报告...
2025-05-21 18:11:51 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 18:25:56 - quantengine - INFO - 数据源加载完成
2025-05-21 18:25:56 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 18:25:56 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 18:25:56 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 18:25:56 - quantengine - INFO - 开始执行回测...
2025-05-21 18:25:56 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 18:25:56 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 18:25:56 - quantengine - INFO - 开始执行策略计算...
2025-05-21 18:25:57 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 18:25:57 - quantengine - INFO - 开始执行回测计算...
2025-05-21 18:26:04 - quantengine - INFO - 回测完成，最终资产:0.75
2025-05-21 18:26:04 - quantengine - INFO - 回测执行完成
2025-05-21 18:26:04 - quantengine - INFO - 开始生成报告...
2025-05-21 18:26:04 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 18:26:08 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 18:26:08 - quantengine.report - INFO - 生成图表...
2025-05-21 18:26:12 - quantengine.report - WARNING - 无法创建 'return' 列，跳过基于收益率的图表
2025-05-21 18:26:12 - quantengine.report - WARNING - 缺少入场/出场价格或时间列，跳过交易价格图绘制
2025-05-21 18:26:17 - quantengine.report - INFO - 报告生成完成
2025-05-21 18:26:17 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 18:27:50 - quantengine - INFO - 数据源加载完成
2025-05-21 18:27:50 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 18:27:50 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 18:27:50 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 18:27:50 - quantengine - INFO - 开始执行回测...
2025-05-21 18:27:50 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 18:27:50 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 18:27:50 - quantengine - INFO - 开始执行策略计算...
2025-05-21 18:27:51 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 18:27:51 - quantengine - INFO - 开始执行回测计算...
2025-05-21 18:27:56 - quantengine - INFO - 回测完成，最终资产:0.16
2025-05-21 18:27:56 - quantengine - INFO - 回测执行完成
2025-05-21 18:27:56 - quantengine - INFO - 开始生成报告...
2025-05-21 18:27:56 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 18:27:59 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 18:27:59 - quantengine.report - INFO - 生成图表...
2025-05-21 18:28:02 - quantengine.report - WARNING - 无法创建 'return' 列，跳过基于收益率的图表
2025-05-21 18:28:02 - quantengine.report - WARNING - 缺少入场/出场价格或时间列，跳过交易价格图绘制
2025-05-21 18:28:06 - quantengine.report - INFO - 报告生成完成
2025-05-21 18:28:06 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 18:30:37 - quantengine - INFO - 数据源加载完成
2025-05-21 18:30:37 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 18:30:37 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 18:30:37 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 18:30:37 - quantengine - INFO - 开始执行回测...
2025-05-21 18:30:37 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 18:30:38 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 18:30:38 - quantengine - INFO - 开始执行策略计算...
2025-05-21 18:30:39 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 18:30:39 - quantengine - INFO - 开始执行回测计算...
2025-05-21 18:30:44 - quantengine - INFO - 回测完成，最终资产:0.75
2025-05-21 18:30:44 - quantengine - INFO - 回测执行完成
2025-05-21 18:30:44 - quantengine - INFO - 开始生成报告...
2025-05-21 18:30:44 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 18:30:48 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 18:30:48 - quantengine.report - INFO - 生成图表...
2025-05-21 18:30:51 - quantengine.report - WARNING - 无法创建 'return' 列，跳过基于收益率的图表
2025-05-21 18:30:51 - quantengine.report - WARNING - 缺少入场/出场价格或时间列，跳过交易价格图绘制
2025-05-21 18:31:31 - quantengine - INFO - 数据源加载完成
2025-05-21 18:31:31 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 18:31:31 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 18:31:31 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 18:31:31 - quantengine - INFO - 开始执行回测...
2025-05-21 18:31:31 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 18:31:31 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 18:31:31 - quantengine - INFO - 开始执行策略计算...
2025-05-21 18:31:32 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 18:31:32 - quantengine - INFO - 开始执行回测计算...
2025-05-21 18:31:37 - quantengine - INFO - 回测完成，最终资产:0.75
2025-05-21 18:31:37 - quantengine - INFO - 回测执行完成
2025-05-21 18:31:37 - quantengine - INFO - 开始生成报告...
2025-05-21 18:31:37 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 18:31:41 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 18:31:41 - quantengine.report - INFO - 生成图表...
2025-05-21 18:31:44 - quantengine.report - WARNING - 无法创建 'return' 列，跳过基于收益率的图表
2025-05-21 18:31:44 - quantengine.report - WARNING - 缺少入场/出场价格或时间列，跳过交易价格图绘制
2025-05-21 18:31:48 - quantengine.report - INFO - 报告生成完成
2025-05-21 18:31:48 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 18:33:04 - quantengine - INFO - 数据源加载完成
2025-05-21 18:33:04 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 18:33:04 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 18:33:04 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 18:33:04 - quantengine - INFO - 开始执行回测...
2025-05-21 18:33:04 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 18:33:04 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 18:33:04 - quantengine - INFO - 开始执行策略计算...
2025-05-21 18:33:05 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 18:33:05 - quantengine - INFO - 开始执行回测计算...
2025-05-21 18:33:09 - quantengine - INFO - 回测完成，最终资产:0.75
2025-05-21 18:33:09 - quantengine - INFO - 回测执行完成
2025-05-21 18:33:09 - quantengine - INFO - 开始生成报告...
2025-05-21 18:33:09 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 18:33:12 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 18:33:12 - quantengine.report - INFO - 生成图表...
2025-05-21 18:33:14 - quantengine.report - WARNING - 无法创建 'return' 列，跳过基于收益率的图表
2025-05-21 18:33:14 - quantengine.report - WARNING - 缺少入场/出场价格或时间列，跳过交易价格图绘制
2025-05-21 18:33:17 - quantengine.report - INFO - 报告生成完成
2025-05-21 18:33:17 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 19:07:08 - quantengine - INFO - 数据源加载完成
2025-05-21 19:07:08 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 19:07:08 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 19:07:08 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 19:07:08 - quantengine - INFO - 开始执行回测...
2025-05-21 19:07:08 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 19:07:08 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 19:07:08 - quantengine - INFO - 开始执行策略计算...
2025-05-21 19:07:09 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 19:07:09 - quantengine - INFO - 开始执行回测计算...
2025-05-21 19:07:14 - quantengine - INFO - 回测完成，最终资产:0.75
2025-05-21 19:07:14 - quantengine - INFO - 回测执行完成
2025-05-21 19:07:14 - quantengine - INFO - 开始生成报告...
2025-05-21 19:07:14 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 19:07:18 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 19:07:18 - quantengine.report - INFO - 报告生成完成
2025-05-21 19:07:18 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 19:16:26 - quantengine - INFO - 数据源加载完成
2025-05-21 19:16:26 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 19:16:26 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 19:16:26 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 19:16:26 - quantengine - INFO - 开始执行回测...
2025-05-21 19:16:26 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 19:16:27 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 19:16:27 - quantengine - INFO - 开始执行策略计算...
2025-05-21 19:16:28 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 19:16:28 - quantengine - INFO - 开始执行回测计算...
2025-05-21 19:16:33 - quantengine - INFO - 回测完成，最终资产:0.75
2025-05-21 19:16:33 - quantengine - INFO - 回测执行完成
2025-05-21 19:16:33 - quantengine - INFO - 开始生成报告...
2025-05-21 19:16:33 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 19:16:36 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 19:17:12 - quantengine - INFO - 数据源加载完成
2025-05-21 19:17:12 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 19:17:12 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 19:17:12 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 19:17:12 - quantengine - INFO - 开始执行回测...
2025-05-21 19:17:12 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 19:17:13 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 19:17:13 - quantengine - INFO - 开始执行策略计算...
2025-05-21 19:17:14 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 19:17:14 - quantengine - INFO - 开始执行回测计算...
2025-05-21 19:17:20 - quantengine - INFO - 回测完成，最终资产:0.75
2025-05-21 19:17:20 - quantengine - INFO - 回测执行完成
2025-05-21 19:17:20 - quantengine - INFO - 开始生成报告...
2025-05-21 19:17:20 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 19:17:24 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 19:24:31 - quantengine - INFO - 数据源加载完成
2025-05-21 19:24:31 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 19:24:31 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 19:24:31 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 19:24:31 - quantengine - INFO - 开始执行回测...
2025-05-21 19:24:31 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 19:24:31 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 19:24:31 - quantengine - INFO - 开始执行策略计算...
2025-05-21 19:24:32 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 19:24:32 - quantengine - INFO - 开始执行回测计算...
2025-05-21 19:24:36 - quantengine - INFO - 回测完成，最终资产:0.75
2025-05-21 19:24:36 - quantengine - INFO - 回测执行完成
2025-05-21 19:24:36 - quantengine - INFO - 开始生成报告...
2025-05-21 19:24:36 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 19:24:39 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 19:24:56 - quantengine - INFO - 数据源加载完成
2025-05-21 19:24:56 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 19:24:56 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 19:24:56 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 19:24:56 - quantengine - INFO - 开始执行回测...
2025-05-21 19:24:56 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 19:24:56 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 19:24:56 - quantengine - INFO - 开始执行策略计算...
2025-05-21 19:24:57 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 19:24:57 - quantengine - INFO - 开始执行回测计算...
2025-05-21 19:25:03 - quantengine - INFO - 回测完成，最终资产:0.75
2025-05-21 19:25:03 - quantengine - INFO - 回测执行完成
2025-05-21 19:25:03 - quantengine - INFO - 开始生成报告...
2025-05-21 19:25:03 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 19:25:06 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 19:26:31 - quantengine - INFO - 数据源加载完成
2025-05-21 19:26:31 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 19:26:31 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 19:26:31 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 19:26:31 - quantengine - INFO - 开始执行回测...
2025-05-21 19:26:31 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 19:26:31 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 19:26:31 - quantengine - INFO - 开始执行策略计算...
2025-05-21 19:26:32 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 19:26:32 - quantengine - INFO - 开始执行回测计算...
2025-05-21 19:26:38 - quantengine - INFO - 回测完成，最终资产:0.75
2025-05-21 19:26:38 - quantengine - INFO - 回测执行完成
2025-05-21 19:26:38 - quantengine - INFO - 开始生成报告...
2025-05-21 19:26:38 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 19:26:41 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 19:26:41 - quantengine.report - INFO - 报告生成完成
2025-05-21 19:26:41 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 19:31:28 - quantengine - INFO - 数据源加载完成
2025-05-21 19:31:28 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 19:31:28 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 19:31:28 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 19:31:28 - quantengine - INFO - 开始执行回测...
2025-05-21 19:31:28 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 19:31:28 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 19:31:28 - quantengine - INFO - 开始执行策略计算...
2025-05-21 19:31:29 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 19:31:29 - quantengine - INFO - 开始执行回测计算...
2025-05-21 19:31:33 - quantengine - INFO - 回测完成，最终资产:0.75
2025-05-21 19:31:33 - quantengine - INFO - 回测执行完成
2025-05-21 19:31:33 - quantengine - INFO - 开始生成报告...
2025-05-21 19:31:33 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 19:31:36 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 19:31:36 - quantengine.report - INFO - 报告生成完成
2025-05-21 19:31:36 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 20:44:28 - quantengine - INFO - 数据源加载完成
2025-05-21 20:44:28 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 20:44:28 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 20:44:28 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 20:44:28 - quantengine - INFO - 开始执行回测...
2025-05-21 20:44:28 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 20:44:28 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 20:44:28 - quantengine - INFO - 开始执行策略计算...
2025-05-21 20:44:29 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 20:44:29 - quantengine - INFO - 开始执行回测计算...
2025-05-21 20:44:33 - quantengine - INFO - 回测完成，最终资产:0.75
2025-05-21 20:44:33 - quantengine - INFO - 回测执行完成
2025-05-21 20:44:33 - quantengine - INFO - 开始生成报告...
2025-05-21 20:44:33 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 20:44:36 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 20:44:36 - quantengine.report - INFO - 报告生成完成
2025-05-21 20:44:36 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 20:50:12 - quantengine - INFO - 数据源加载完成
2025-05-21 20:50:12 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 20:50:12 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 20:50:12 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 20:50:12 - quantengine - INFO - 开始执行回测...
2025-05-21 20:50:12 - quantengine - INFO - 开始获取sz000001数据，时间范围:2020-01-01至2021-01-01
2025-05-21 20:50:12 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 20:50:12 - quantengine - INFO - 开始执行策略计算...
2025-05-21 20:50:13 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 20:50:13 - quantengine - INFO - 开始执行回测计算...
2025-05-21 20:50:18 - quantengine - INFO - 回测完成，最终资产:0.75
2025-05-21 20:50:18 - quantengine - INFO - 回测执行完成
2025-05-21 20:50:18 - quantengine - INFO - 开始生成报告...
2025-05-21 20:50:18 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 20:50:21 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 20:50:21 - quantengine.report - INFO - 报告生成完成
2025-05-21 20:50:21 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 20:51:15 - quantengine - INFO - 数据源加载完成
2025-05-21 20:51:15 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 20:51:15 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 20:51:15 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 20:51:15 - quantengine - INFO - 开始执行回测...
2025-05-21 20:51:15 - quantengine - INFO - 开始获取sz000002数据，时间范围:2020-01-01至2021-01-01
2025-05-21 20:51:15 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 20:51:15 - quantengine - INFO - 开始执行策略计算...
2025-05-21 20:51:16 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 20:51:16 - quantengine - INFO - 开始执行回测计算...
2025-05-21 20:51:21 - quantengine - INFO - 回测完成，最终资产:-3.64
2025-05-21 20:51:21 - quantengine - INFO - 回测执行完成
2025-05-21 20:51:21 - quantengine - INFO - 开始生成报告...
2025-05-21 20:51:21 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 20:51:25 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 20:51:25 - quantengine.report - INFO - 报告生成完成
2025-05-21 20:51:25 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 20:54:33 - quantengine - INFO - 数据源加载完成
2025-05-21 20:54:33 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 20:54:33 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 20:54:33 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 20:54:33 - quantengine - INFO - 开始执行回测...
2025-05-21 20:54:33 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2021-01-01
2025-05-21 20:54:33 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 20:54:33 - quantengine - INFO - 开始执行策略计算...
2025-05-21 20:54:34 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 20:54:34 - quantengine - INFO - 开始执行回测计算...
2025-05-21 20:54:43 - quantengine - INFO - 回测完成，最终资产:3.77
2025-05-21 20:54:43 - quantengine - INFO - 回测执行完成
2025-05-21 20:54:43 - quantengine - INFO - 开始生成报告...
2025-05-21 20:54:43 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 20:54:48 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 20:54:48 - quantengine.report - INFO - 报告生成完成
2025-05-21 20:54:48 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 21:05:20 - quantengine - INFO - 数据源加载完成
2025-05-21 21:05:20 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 21:05:20 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 21:05:20 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 21:05:20 - quantengine - INFO - 开始执行回测...
2025-05-21 21:05:20 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2021-01-01
2025-05-21 21:05:20 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 21:05:20 - quantengine - INFO - 开始执行策略计算...
2025-05-21 21:05:20 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 21:05:20 - quantengine - INFO - 开始执行回测计算...
2025-05-21 21:05:25 - quantengine - INFO - 回测完成，最终资产:3.77
2025-05-21 21:05:25 - quantengine - INFO - 回测执行完成
2025-05-21 21:05:25 - quantengine - INFO - 开始生成报告...
2025-05-21 21:05:25 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 21:05:28 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 21:05:28 - quantengine.report - INFO - 报告生成完成
2025-05-21 21:05:28 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 21:08:21 - quantengine - INFO - 数据源加载完成
2025-05-21 21:08:21 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 21:08:21 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 21:08:21 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 21:08:21 - quantengine - INFO - 开始执行回测...
2025-05-21 21:08:21 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2021-01-01
2025-05-21 21:08:21 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 21:08:21 - quantengine - INFO - 开始执行策略计算...
2025-05-21 21:08:22 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 21:08:22 - quantengine - INFO - 开始执行回测计算...
2025-05-21 21:08:27 - quantengine - INFO - 回测完成，最终资产:75.31
2025-05-21 21:08:27 - quantengine - INFO - 回测执行完成
2025-05-21 21:08:27 - quantengine - INFO - 开始生成报告...
2025-05-21 21:08:27 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 21:08:30 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 21:08:30 - quantengine.report - INFO - 报告生成完成
2025-05-21 21:08:30 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 21:08:55 - quantengine - INFO - 数据源加载完成
2025-05-21 21:08:55 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 21:08:55 - quantengine - INFO - 策略加载成功，参数: {'fast_window': 10, 'mid_window': 30, 'slow_window': 90}
2025-05-21 21:08:55 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 21:08:55 - quantengine - INFO - 开始执行回测...
2025-05-21 21:08:55 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2021-01-01
2025-05-21 21:08:55 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 21:08:55 - quantengine - INFO - 开始执行策略计算...
2025-05-21 21:08:56 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 21:08:56 - quantengine - INFO - 开始执行回测计算...
2025-05-21 21:09:01 - quantengine - INFO - 回测完成，最终资产:75.31
2025-05-21 21:09:01 - quantengine - INFO - 回测执行完成
2025-05-21 21:09:01 - quantengine - INFO - 开始生成报告...
2025-05-21 21:09:01 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 21:09:04 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 21:09:04 - quantengine.report - INFO - 报告生成完成
2025-05-21 21:09:04 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 21:11:44 - quantengine - INFO - 数据源加载完成
2025-05-21 21:11:44 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 21:11:44 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 21:11:44 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 21:11:44 - quantengine - INFO - 开始执行回测...
2025-05-21 21:11:44 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-21 21:11:44 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 21:11:44 - quantengine - INFO - 开始执行策略计算...
2025-05-21 21:12:16 - quantengine - INFO - 数据源加载完成
2025-05-21 21:12:16 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 21:12:16 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 21:12:16 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 21:12:16 - quantengine - INFO - 开始执行回测...
2025-05-21 21:12:16 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-21 21:12:16 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 21:12:16 - quantengine - INFO - 开始执行策略计算...
2025-05-21 21:12:17 - quantengine.strategy - INFO - Generated 90 entry signals and 80 exit signals
2025-05-21 21:12:17 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 21:12:17 - quantengine - INFO - 开始执行回测计算...
2025-05-21 21:12:24 - quantengine - INFO - 回测完成，最终资产:98.62
2025-05-21 21:12:24 - quantengine - INFO - 回测执行完成
2025-05-21 21:12:24 - quantengine - INFO - 开始生成报告...
2025-05-21 21:12:24 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 21:12:29 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 21:12:29 - quantengine.report - INFO - 报告生成完成
2025-05-21 21:12:29 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 21:13:33 - quantengine - INFO - 数据源加载完成
2025-05-21 21:13:33 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 21:13:33 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 21:13:33 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 21:13:33 - quantengine - INFO - 开始执行回测...
2025-05-21 21:13:33 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-21 21:13:34 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 21:13:34 - quantengine - INFO - 开始执行策略计算...
2025-05-21 21:13:35 - quantengine.strategy - INFO - Generated 90 entry signals and 80 exit signals
2025-05-21 21:13:35 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 21:13:35 - quantengine - INFO - 开始执行回测计算...
2025-05-21 21:13:39 - quantengine - INFO - 回测完成，最终资产:98.62
2025-05-21 21:13:39 - quantengine - INFO - 回测执行完成
2025-05-21 21:13:39 - quantengine - INFO - 开始生成报告...
2025-05-21 21:13:39 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 21:13:42 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 21:13:42 - quantengine.report - INFO - 报告生成完成
2025-05-21 21:13:42 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 21:19:58 - quantengine - INFO - 数据源加载完成
2025-05-21 21:19:58 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 21:19:58 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 21:19:58 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 21:19:58 - quantengine - INFO - 开始执行回测...
2025-05-21 21:19:58 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-21 21:19:58 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 21:19:58 - quantengine - INFO - 开始执行策略计算...
2025-05-21 21:19:59 - quantengine.strategy - INFO - Generated 90 entry signals and 80 exit signals
2025-05-21 21:19:59 - quantengine - INFO - 策略计算完成，生成243个入场信号和243个出场信号
2025-05-21 21:19:59 - quantengine - INFO - 开始执行回测计算...
2025-05-21 21:20:04 - quantengine - INFO - 回测完成，最终资产:98.62
2025-05-21 21:20:04 - quantengine - INFO - 回测执行完成
2025-05-21 21:20:04 - quantengine - INFO - 开始生成报告...
2025-05-21 21:20:04 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 21:20:08 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 21:20:08 - quantengine.report - INFO - 报告生成完成
2025-05-21 21:20:08 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 21:20:51 - quantengine - INFO - 数据源加载完成
2025-05-21 21:20:51 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 21:20:51 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 21:20:51 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 21:20:51 - quantengine - INFO - 开始执行回测...
2025-05-21 21:20:51 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2021-12-31
2025-05-21 21:20:51 - quantengine - INFO - 数据获取完成，共486条记录
2025-05-21 21:20:51 - quantengine - INFO - 开始执行策略计算...
2025-05-21 21:20:52 - quantengine.strategy - INFO - Generated 150 entry signals and 202 exit signals
2025-05-21 21:20:52 - quantengine - INFO - 策略计算完成，生成486个入场信号和486个出场信号
2025-05-21 21:20:52 - quantengine - INFO - 开始执行回测计算...
2025-05-21 21:20:58 - quantengine - INFO - 回测完成，最终资产:96.24
2025-05-21 21:20:58 - quantengine - INFO - 回测执行完成
2025-05-21 21:20:58 - quantengine - INFO - 开始生成报告...
2025-05-21 21:20:58 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 21:21:01 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 21:21:01 - quantengine.report - INFO - 报告生成完成
2025-05-21 21:21:01 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 21:27:12 - quantengine - INFO - 数据源加载完成
2025-05-21 21:27:12 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 21:27:12 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 21:27:12 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 21:27:12 - quantengine - INFO - 开始执行回测...
2025-05-21 21:27:12 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2021-12-31
2025-05-21 21:27:12 - quantengine - INFO - 数据获取完成，共486条记录
2025-05-21 21:27:12 - quantengine - INFO - 开始执行策略计算...
2025-05-21 21:27:13 - quantengine.strategy - INFO - Generated 150 entry signals and 202 exit signals
2025-05-21 21:27:13 - quantengine - INFO - 策略计算完成，生成150个入场信号和202个出场信号
2025-05-21 21:27:13 - quantengine - INFO - 开始执行回测计算...
2025-05-21 21:27:18 - quantengine - INFO - 回测完成，最终资产:96.24
2025-05-21 21:27:18 - quantengine - INFO - 回测执行完成
2025-05-21 21:27:18 - quantengine - INFO - 开始生成报告...
2025-05-21 21:27:18 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 21:27:22 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 21:27:22 - quantengine.report - INFO - 报告生成完成
2025-05-21 21:27:22 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 21:57:16 - quantengine - INFO - 数据源加载完成
2025-05-21 21:57:16 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 21:57:16 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 21:57:16 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 21:57:16 - quantengine - INFO - 开始执行回测...
2025-05-21 21:57:16 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2021-12-31
2025-05-21 21:57:16 - quantengine - INFO - 数据获取完成，共486条记录
2025-05-21 21:57:16 - quantengine - INFO - 开始执行策略计算...
2025-05-21 21:57:17 - quantengine.strategy - INFO - Generated 150 entry signals and 202 exit signals
2025-05-21 21:57:17 - quantengine - INFO - 策略计算完成，生成150个入场信号和202个出场信号
2025-05-21 21:57:17 - quantengine - INFO - 开始执行回测计算...
2025-05-21 21:57:22 - quantengine - INFO - 回测完成，最终资产:96.24
2025-05-21 21:57:22 - quantengine - INFO - 回测执行完成
2025-05-21 21:57:22 - quantengine - INFO - 开始生成报告...
2025-05-21 21:57:22 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 21:57:24 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 21:57:24 - quantengine.report - INFO - 报告生成完成
2025-05-21 21:57:24 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 21:59:25 - quantengine - INFO - 数据源加载完成
2025-05-21 21:59:25 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 21:59:25 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 21:59:25 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 21:59:25 - quantengine - INFO - 开始执行回测...
2025-05-21 21:59:25 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2021-12-31
2025-05-21 21:59:25 - quantengine - INFO - 数据获取完成，共486条记录
2025-05-21 21:59:25 - quantengine - INFO - 开始执行策略计算...
2025-05-21 21:59:26 - quantengine.strategy - INFO - Generated 150 entry signals and 202 exit signals
2025-05-21 21:59:26 - quantengine - INFO - 策略计算完成，生成150个入场信号和202个出场信号
2025-05-21 21:59:26 - quantengine - INFO - 开始执行回测计算...
2025-05-21 21:59:33 - quantengine - INFO - 回测完成，最终资产:481.21
2025-05-21 21:59:33 - quantengine - INFO - 回测执行完成
2025-05-21 21:59:33 - quantengine - INFO - 开始生成报告...
2025-05-21 21:59:33 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 21:59:37 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 21:59:37 - quantengine.report - INFO - 报告生成完成
2025-05-21 21:59:37 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 22:05:17 - quantengine - INFO - 数据源加载完成
2025-05-21 22:05:17 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 22:05:17 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 22:05:17 - quantengine - INFO - 正在创建输出目录: output
2025-05-21 22:05:17 - quantengine - INFO - 开始执行回测...
2025-05-21 22:05:17 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2023-12-31
2025-05-21 22:05:17 - quantengine - INFO - 数据获取完成，共970条记录
2025-05-21 22:05:17 - quantengine - INFO - 开始执行策略计算...
2025-05-21 22:05:18 - quantengine.strategy - INFO - Generated 468 entry signals and 483 exit signals
2025-05-21 22:05:18 - quantengine - INFO - 策略计算完成，生成468个入场信号和483个出场信号
2025-05-21 22:05:18 - quantengine - INFO - 开始执行回测计算...
2025-05-21 22:05:26 - quantengine - INFO - 回测完成，最终资产:966.88
2025-05-21 22:05:26 - quantengine - INFO - 回测执行完成
2025-05-21 22:05:26 - quantengine - INFO - 开始生成报告...
2025-05-21 22:05:26 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 22:05:31 - quantengine.report - INFO - 将报告保存到 output
2025-05-21 22:05:31 - quantengine.report - INFO - 报告生成完成
2025-05-21 22:05:31 - quantengine - INFO - 报告生成完成，已保存至 output
2025-05-21 22:06:17 - quantengine - INFO - 数据源加载完成
2025-05-21 22:06:17 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 22:06:17 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 22:06:17 - quantengine - INFO - 正在创建输出目录: output
2025-05-21 22:06:17 - quantengine - INFO - 开始执行回测...
2025-05-21 22:06:17 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2023-12-31
2025-05-21 22:06:17 - quantengine - INFO - 数据获取完成，共970条记录
2025-05-21 22:06:17 - quantengine - INFO - 开始执行策略计算...
2025-05-21 22:06:19 - quantengine.strategy - INFO - Generated 468 entry signals and 483 exit signals
2025-05-21 22:06:19 - quantengine - INFO - 策略计算完成，生成468个入场信号和483个出场信号
2025-05-21 22:06:19 - quantengine - INFO - 开始执行回测计算...
2025-05-21 22:06:29 - quantengine - INFO - 回测完成，最终资产:966.88
2025-05-21 22:06:29 - quantengine - INFO - 回测执行完成
2025-05-21 22:06:29 - quantengine - INFO - 开始生成报告...
2025-05-21 22:06:29 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 22:06:36 - quantengine.report - INFO - 将报告保存到 output
2025-05-21 22:06:36 - quantengine.report - INFO - 报告生成完成
2025-05-21 22:06:36 - quantengine - INFO - 报告生成完成，已保存至 output
2025-05-21 22:07:17 - quantengine - INFO - 数据源加载完成
2025-05-21 22:07:17 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 22:07:17 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 22:07:17 - quantengine - INFO - 正在创建输出目录: output
2025-05-21 22:07:17 - quantengine - INFO - 开始执行回测...
2025-05-21 22:07:17 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2023-12-31
2025-05-21 22:07:17 - quantengine - INFO - 数据获取完成，共970条记录
2025-05-21 22:07:17 - quantengine - INFO - 开始执行策略计算...
2025-05-21 22:07:20 - quantengine.strategy - INFO - Generated 468 entry signals and 483 exit signals
2025-05-21 22:07:20 - quantengine - INFO - 策略计算完成，生成468个入场信号和483个出场信号
2025-05-21 22:07:20 - quantengine - INFO - 开始执行回测计算...
2025-05-21 22:07:29 - quantengine - INFO - 回测完成，最终资产:106974.03
2025-05-21 22:07:29 - quantengine - INFO - 回测执行完成
2025-05-21 22:07:29 - quantengine - INFO - 开始生成报告...
2025-05-21 22:07:29 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 22:07:34 - quantengine.report - INFO - 将报告保存到 output
2025-05-21 22:07:34 - quantengine.report - INFO - 报告生成完成
2025-05-21 22:07:34 - quantengine - INFO - 报告生成完成，已保存至 output
2025-05-21 22:12:23 - quantengine - INFO - 数据源加载完成
2025-05-21 22:12:23 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 22:12:23 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 22:12:23 - quantengine - INFO - 正在创建输出目录: output
2025-05-21 22:12:23 - quantengine - INFO - 开始执行回测...
2025-05-21 22:12:23 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2023-12-31
2025-05-21 22:12:23 - quantengine - INFO - 数据获取完成，共970条记录
2025-05-21 22:12:23 - quantengine - INFO - 开始执行策略计算...
2025-05-21 22:12:24 - quantengine.strategy - INFO - Generated 468 entry signals and 483 exit signals
2025-05-21 22:12:24 - quantengine - INFO - 策略计算完成，生成468个入场信号和483个出场信号
2025-05-21 22:12:24 - quantengine - INFO - 开始执行回测计算...
2025-05-21 22:12:30 - quantengine - INFO - 回测完成，最终资产:106974.03
2025-05-21 22:12:30 - quantengine - INFO - 回测执行完成
2025-05-21 22:12:30 - quantengine - INFO - 开始生成报告...
2025-05-21 22:12:30 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 22:12:33 - quantengine.report - INFO - 将报告保存到 output
2025-05-21 22:12:34 - quantengine.report - INFO - 报告生成完成
2025-05-21 22:12:34 - quantengine - INFO - 报告生成完成，已保存至 output
2025-05-21 22:18:04 - quantengine - INFO - 数据源加载完成
2025-05-21 22:18:04 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 22:18:04 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 22:18:04 - quantengine - INFO - 正在创建输出目录: output
2025-05-21 22:18:04 - quantengine - INFO - 开始执行回测...
2025-05-21 22:18:04 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2023-12-31
2025-05-21 22:18:04 - quantengine - INFO - 数据获取完成，共970条记录
2025-05-21 22:18:04 - quantengine - INFO - 开始执行策略计算...
2025-05-21 22:18:06 - quantengine.strategy - INFO - Generated 468 entry signals and 483 exit signals
2025-05-21 22:18:06 - quantengine - INFO - 策略计算完成，生成468个入场信号和483个出场信号
2025-05-21 22:18:06 - quantengine - INFO - 开始执行回测计算...
2025-05-21 22:18:14 - quantengine - INFO - 回测完成，最终资产:106974.03
2025-05-21 22:18:14 - quantengine - INFO - 回测执行完成
2025-05-21 22:18:14 - quantengine - INFO - 开始生成报告...
2025-05-21 22:18:14 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 22:18:19 - quantengine.report - INFO - 将报告保存到 output
2025-05-21 22:18:19 - quantengine.report - INFO - 报告生成完成
2025-05-21 22:18:19 - quantengine - INFO - 报告生成完成，已保存至 output
2025-05-21 22:18:58 - quantengine - INFO - 数据源加载完成
2025-05-21 22:18:58 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 22:18:58 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 22:18:58 - quantengine - INFO - 正在创建输出目录: output
2025-05-21 22:18:58 - quantengine - INFO - 开始执行回测...
2025-05-21 22:18:58 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2023-12-31
2025-05-21 22:18:58 - quantengine - INFO - 数据获取完成，共970条记录
2025-05-21 22:18:58 - quantengine - INFO - 开始执行策略计算...
2025-05-21 22:18:59 - quantengine.strategy - INFO - Generated 468 entry signals and 483 exit signals
2025-05-21 22:18:59 - quantengine - INFO - 策略计算完成，生成468个入场信号和483个出场信号
2025-05-21 22:18:59 - quantengine - INFO - 开始执行回测计算...
2025-05-21 22:19:09 - quantengine - INFO - 回测完成，最终资产:106974.03
2025-05-21 22:19:09 - quantengine - INFO - 回测执行完成
2025-05-21 22:19:09 - quantengine - INFO - 开始生成报告...
2025-05-21 22:19:09 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 22:19:14 - quantengine.report - INFO - 将报告保存到 output
2025-05-21 22:19:14 - quantengine.report - INFO - 报告生成完成
2025-05-21 22:19:14 - quantengine - INFO - 报告生成完成，已保存至 output
2025-05-21 22:22:01 - quantengine - INFO - 数据源加载完成
2025-05-21 22:22:01 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 22:22:01 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 22:22:01 - quantengine - INFO - 正在创建输出目录: output
2025-05-21 22:22:01 - quantengine - INFO - 开始执行回测...
2025-05-21 22:22:01 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2023-12-31
2025-05-21 22:22:01 - quantengine - INFO - 数据获取完成，共970条记录
2025-05-21 22:22:01 - quantengine - INFO - 开始执行策略计算...
2025-05-21 22:22:02 - quantengine.strategy - INFO - Generated 468 entry signals and 483 exit signals
2025-05-21 22:22:02 - quantengine - INFO - 策略计算完成，生成468个入场信号和483个出场信号
2025-05-21 22:22:02 - quantengine - INFO - 开始执行回测计算...
2025-05-21 22:22:10 - quantengine - INFO - 生成了 24 条交易记录
2025-05-21 22:22:10 - quantengine - INFO - 回测完成，最终资产:106974.03
2025-05-21 22:22:10 - quantengine - INFO - 回测执行完成
2025-05-21 22:22:10 - quantengine - INFO - 开始生成报告...
2025-05-21 22:22:10 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 22:22:52 - quantengine - INFO - 数据源加载完成
2025-05-21 22:22:52 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 22:22:52 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 22:22:52 - quantengine - INFO - 正在创建输出目录: output
2025-05-21 22:22:52 - quantengine - INFO - 开始执行回测...
2025-05-21 22:22:52 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2023-12-31
2025-05-21 22:22:53 - quantengine - INFO - 数据获取完成，共970条记录
2025-05-21 22:22:53 - quantengine - INFO - 开始执行策略计算...
2025-05-21 22:22:54 - quantengine.strategy - INFO - Generated 468 entry signals and 483 exit signals
2025-05-21 22:22:54 - quantengine - INFO - 策略计算完成，生成468个入场信号和483个出场信号
2025-05-21 22:22:54 - quantengine - INFO - 开始执行回测计算...
2025-05-21 22:23:01 - quantengine - INFO - 生成了 24 条交易记录
2025-05-21 22:23:01 - quantengine - INFO - 回测完成，最终资产:106974.03
2025-05-21 22:23:01 - quantengine - INFO - 回测执行完成
2025-05-21 22:23:01 - quantengine - INFO - 开始生成报告...
2025-05-21 22:23:01 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 22:23:06 - quantengine.report - INFO - 将报告保存到 output
2025-05-21 22:23:06 - quantengine.report - INFO - 报告生成完成
2025-05-21 22:23:06 - quantengine - INFO - 报告生成完成，已保存至 output
2025-05-21 22:24:29 - quantengine - INFO - 数据源加载完成
2025-05-21 22:24:29 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 22:24:29 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 22:24:29 - quantengine - INFO - 正在创建输出目录: output
2025-05-21 22:24:29 - quantengine - INFO - 开始执行回测...
2025-05-21 22:24:29 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2023-12-31
2025-05-21 22:24:30 - quantengine - INFO - 数据获取完成，共970条记录
2025-05-21 22:24:30 - quantengine - INFO - 开始执行策略计算...
2025-05-21 22:24:31 - quantengine.strategy - INFO - Generated 468 entry signals and 483 exit signals
2025-05-21 22:24:31 - quantengine - INFO - 策略计算完成，生成468个入场信号和483个出场信号
2025-05-21 22:24:31 - quantengine - INFO - 开始执行回测计算...
2025-05-21 22:24:39 - quantengine - INFO - 生成了 24 条交易记录
2025-05-21 22:24:39 - quantengine - INFO - 回测完成，最终资产:106974.03
2025-05-21 22:24:39 - quantengine - INFO - 回测执行完成
2025-05-21 22:24:39 - quantengine - INFO - 开始生成报告...
2025-05-21 22:24:39 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 22:24:43 - quantengine.report - INFO - 将报告保存到 output
2025-05-21 22:24:44 - quantengine.report - INFO - 报告生成完成
2025-05-21 22:24:44 - quantengine - INFO - 报告生成完成，已保存至 output
2025-05-21 22:26:49 - quantengine - INFO - 数据源加载完成
2025-05-21 22:26:49 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 22:26:49 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 22:26:49 - quantengine - INFO - 正在创建输出目录: output
2025-05-21 22:26:49 - quantengine - INFO - 开始执行回测...
2025-05-21 22:26:49 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2023-12-31
2025-05-21 22:26:49 - quantengine - INFO - 数据获取完成，共970条记录
2025-05-21 22:26:49 - quantengine - INFO - 开始执行策略计算...
2025-05-21 22:26:51 - quantengine.strategy - INFO - Generated 468 entry signals and 483 exit signals
2025-05-21 22:26:51 - quantengine - INFO - 策略计算完成，生成468个入场信号和483个出场信号
2025-05-21 22:26:51 - quantengine - INFO - 开始执行回测计算...
2025-05-21 22:26:58 - quantengine - INFO - 生成了 24 条交易记录
2025-05-21 22:26:58 - quantengine - INFO - 回测完成，最终资产:106974.03
2025-05-21 22:26:58 - quantengine - INFO - 回测执行完成
2025-05-21 22:26:58 - quantengine - INFO - 开始生成报告...
2025-05-21 22:26:58 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 22:27:03 - quantengine.report - INFO - 将报告保存到 output
2025-05-21 22:27:03 - quantengine.report - INFO - 报告生成完成
2025-05-21 22:27:03 - quantengine - INFO - 报告生成完成，已保存至 output
2025-05-21 22:29:36 - quantengine - INFO - 数据源加载完成
2025-05-21 22:29:36 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 22:29:36 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 22:29:36 - quantengine - INFO - 正在创建输出目录: output
2025-05-21 22:29:36 - quantengine - INFO - 开始执行回测...
2025-05-21 22:29:36 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2023-12-31
2025-05-21 22:29:36 - quantengine - INFO - 数据获取完成，共970条记录
2025-05-21 22:29:36 - quantengine - INFO - 开始执行策略计算...
2025-05-21 22:29:37 - quantengine.strategy - INFO - Generated 468 entry signals and 483 exit signals
2025-05-21 22:29:37 - quantengine - INFO - 策略计算完成，生成468个入场信号和483个出场信号
2025-05-21 22:29:37 - quantengine - INFO - 开始执行回测计算...
2025-05-21 22:29:45 - quantengine - INFO - 生成了 24 条交易记录
2025-05-21 22:29:45 - quantengine - INFO - 回测完成，最终资产:106974.03
2025-05-21 22:29:45 - quantengine - INFO - 回测执行完成
2025-05-21 22:29:45 - quantengine - INFO - 开始生成报告...
2025-05-21 22:29:45 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 22:29:49 - quantengine.report - INFO - 将报告保存到 output
2025-05-21 22:29:49 - quantengine.report - INFO - 报告生成完成
2025-05-21 22:29:49 - quantengine - INFO - 报告生成完成，已保存至 output
2025-05-21 22:40:11 - quantengine - INFO - 数据源加载完成
2025-05-21 22:40:11 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 22:40:11 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 22:40:11 - quantengine - INFO - 正在创建输出目录: output
2025-05-21 22:40:11 - quantengine - INFO - 开始执行回测...
2025-05-21 22:40:11 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2023-12-31
2025-05-21 22:40:11 - quantengine - INFO - 数据获取完成，共970条记录
2025-05-21 22:40:11 - quantengine - INFO - 开始执行策略计算...
2025-05-21 22:40:12 - quantengine.strategy - INFO - Generated 468 entry signals and 483 exit signals
2025-05-21 22:40:12 - quantengine - INFO - 策略计算完成，生成468个入场信号和483个出场信号
2025-05-21 22:40:12 - quantengine - INFO - 开始执行回测计算...
2025-05-21 22:40:19 - quantengine - INFO - 生成了 24 条交易记录
2025-05-21 22:40:19 - quantengine - INFO - 回测完成，最终资产:106974.03
2025-05-21 22:40:19 - quantengine - INFO - 回测执行完成
2025-05-21 22:40:19 - quantengine - INFO - 开始生成报告...
2025-05-21 22:40:19 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 22:41:47 - quantengine - INFO - 数据源加载完成
2025-05-21 22:41:47 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 22:41:47 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 22:41:47 - quantengine - INFO - 正在创建输出目录: output
2025-05-21 22:41:47 - quantengine - INFO - 开始执行回测...
2025-05-21 22:41:47 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2023-12-31
2025-05-21 22:41:48 - quantengine - INFO - 数据获取完成，共970条记录
2025-05-21 22:41:48 - quantengine - INFO - 开始执行策略计算...
2025-05-21 22:41:50 - quantengine.strategy - INFO - Generated 468 entry signals and 483 exit signals
2025-05-21 22:41:50 - quantengine - INFO - 策略计算完成，生成468个入场信号和483个出场信号
2025-05-21 22:41:50 - quantengine - INFO - 开始执行回测计算...
2025-05-21 22:42:00 - quantengine - INFO - 生成了 24 条交易记录
2025-05-21 22:42:00 - quantengine - INFO - 回测完成，最终资产:106974.03
2025-05-21 22:42:00 - quantengine - INFO - 回测执行完成
2025-05-21 22:42:00 - quantengine - INFO - 开始生成报告...
2025-05-21 22:42:00 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 22:42:40 - quantengine - INFO - 数据源加载完成
2025-05-21 22:42:40 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 22:42:40 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 22:42:40 - quantengine - INFO - 正在创建输出目录: output
2025-05-21 22:42:40 - quantengine - INFO - 开始执行回测...
2025-05-21 22:42:40 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2023-12-31
2025-05-21 22:42:40 - quantengine - INFO - 数据获取完成，共970条记录
2025-05-21 22:42:40 - quantengine - INFO - 开始执行策略计算...
2025-05-21 22:42:42 - quantengine.strategy - INFO - Generated 468 entry signals and 483 exit signals
2025-05-21 22:42:42 - quantengine - INFO - 策略计算完成，生成468个入场信号和483个出场信号
2025-05-21 22:42:42 - quantengine - INFO - 开始执行回测计算...
2025-05-21 22:42:51 - quantengine - INFO - 生成了 24 条交易记录
2025-05-21 22:42:51 - quantengine - INFO - 回测完成，最终资产:106974.03
2025-05-21 22:42:51 - quantengine - INFO - 回测执行完成
2025-05-21 22:42:51 - quantengine - INFO - 开始生成报告...
2025-05-21 22:42:51 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 22:43:24 - quantengine - INFO - 数据源加载完成
2025-05-21 22:43:24 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 22:43:24 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 22:43:24 - quantengine - INFO - 正在创建输出目录: output
2025-05-21 22:43:24 - quantengine - INFO - 开始执行回测...
2025-05-21 22:43:24 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2023-12-31
2025-05-21 22:43:24 - quantengine - INFO - 数据获取完成，共970条记录
2025-05-21 22:43:24 - quantengine - INFO - 开始执行策略计算...
2025-05-21 22:43:26 - quantengine.strategy - INFO - Generated 468 entry signals and 483 exit signals
2025-05-21 22:43:26 - quantengine - INFO - 策略计算完成，生成468个入场信号和483个出场信号
2025-05-21 22:43:26 - quantengine - INFO - 开始执行回测计算...
2025-05-21 22:43:37 - quantengine - INFO - 生成了 24 条交易记录
2025-05-21 22:43:37 - quantengine - INFO - 回测完成，最终资产:106974.03
2025-05-21 22:43:37 - quantengine - INFO - 回测执行完成
2025-05-21 22:43:37 - quantengine - INFO - 开始生成报告...
2025-05-21 22:43:37 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 22:43:43 - quantengine.report - INFO - 将报告保存到 output
2025-05-21 22:44:58 - quantengine - INFO - 数据源加载完成
2025-05-21 22:44:58 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 22:44:58 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 22:44:58 - quantengine - INFO - 正在创建输出目录: output
2025-05-21 22:44:58 - quantengine - INFO - 开始执行回测...
2025-05-21 22:44:58 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2023-12-31
2025-05-21 22:44:58 - quantengine - INFO - 数据获取完成，共970条记录
2025-05-21 22:44:58 - quantengine - INFO - 开始执行策略计算...
2025-05-21 22:45:00 - quantengine.strategy - INFO - Generated 468 entry signals and 483 exit signals
2025-05-21 22:45:00 - quantengine - INFO - 策略计算完成，生成468个入场信号和483个出场信号
2025-05-21 22:45:00 - quantengine - INFO - 开始执行回测计算...
2025-05-21 22:45:10 - quantengine - INFO - 生成了 24 条交易记录
2025-05-21 22:45:10 - quantengine - INFO - 回测完成，最终资产:106974.03
2025-05-21 22:45:10 - quantengine - INFO - 回测执行完成
2025-05-21 22:45:10 - quantengine - INFO - 开始生成报告...
2025-05-21 22:45:10 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 22:45:17 - quantengine.report - INFO - 将报告保存到 output
2025-05-21 22:47:16 - quantengine - INFO - 数据源加载完成
2025-05-21 22:47:16 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 22:47:16 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 22:47:16 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 22:47:16 - quantengine - INFO - 开始执行回测...
2025-05-21 22:47:16 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2021-01-01
2025-05-21 22:47:16 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 22:47:16 - quantengine - INFO - 开始执行策略计算...
2025-05-21 22:47:18 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-21 22:47:18 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-21 22:47:18 - quantengine - INFO - 开始执行回测计算...
2025-05-21 22:47:28 - quantengine - INFO - 生成了 5 条交易记录
2025-05-21 22:47:28 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-21 22:47:28 - quantengine - INFO - 回测执行完成
2025-05-21 22:47:28 - quantengine - INFO - 开始生成报告...
2025-05-21 22:47:28 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 22:47:35 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 22:48:04 - quantengine - INFO - 数据源加载完成
2025-05-21 22:48:04 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 22:48:04 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 22:48:04 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 22:48:04 - quantengine - INFO - 开始执行回测...
2025-05-21 22:48:04 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2021-01-01
2025-05-21 22:48:05 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 22:48:05 - quantengine - INFO - 开始执行策略计算...
2025-05-21 22:48:06 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-21 22:48:06 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-21 22:48:06 - quantengine - INFO - 开始执行回测计算...
2025-05-21 22:48:16 - quantengine - INFO - 生成了 5 条交易记录
2025-05-21 22:48:16 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-21 22:48:16 - quantengine - INFO - 回测执行完成
2025-05-21 22:48:16 - quantengine - INFO - 开始生成报告...
2025-05-21 22:48:16 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 22:48:23 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 22:48:51 - quantengine - INFO - 数据源加载完成
2025-05-21 22:48:51 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 22:48:51 - quantengine - INFO - 策略加载成功，参数: {}
2025-05-21 22:48:51 - quantengine - INFO - 正在创建输出目录: ./output
2025-05-21 22:48:51 - quantengine - INFO - 开始执行回测...
2025-05-21 22:48:51 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2021-01-01
2025-05-21 22:48:51 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-21 22:48:51 - quantengine - INFO - 开始执行策略计算...
2025-05-21 22:48:53 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-21 22:48:53 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-21 22:48:53 - quantengine - INFO - 开始执行回测计算...
2025-05-21 22:49:04 - quantengine - INFO - 生成了 5 条交易记录
2025-05-21 22:49:04 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-21 22:49:04 - quantengine - INFO - 回测执行完成
2025-05-21 22:49:04 - quantengine - INFO - 开始生成报告...
2025-05-21 22:49:04 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 22:49:10 - quantengine.report - INFO - 将报告保存到 ./output
2025-05-21 22:49:11 - quantengine.report - INFO - 报告生成完成
2025-05-21 22:49:11 - quantengine - INFO - 报告生成完成，已保存至 ./output
2025-05-21 22:58:37 - quantengine - INFO - 数据源加载完成
2025-05-21 22:58:37 - quantengine - INFO - 正在加载策略: MACrossStrategy
2025-05-21 22:58:37 - quantengine - ERROR - 策略加载失败: No module named 'quantengine.strategies.MACrossStrategy'
2025-05-21 22:59:11 - quantengine - INFO - 数据源加载完成
2025-05-21 22:59:11 - quantengine - INFO - 正在加载策略: MACrossStrategy
2025-05-21 22:59:11 - quantengine - ERROR - 策略加载失败: No module named 'quantengine.strategies.macross_strategy'
2025-05-21 23:00:25 - quantengine - INFO - 数据源加载完成
2025-05-21 23:00:25 - quantengine - INFO - 正在加载策略: MACrossStrategy
2025-05-21 23:00:25 - quantengine - ERROR - 策略加载失败: No module named 'quantengine.strategies.MACrossStrategy'
2025-05-21 23:02:23 - quantengine - INFO - 数据源加载完成
2025-05-21 23:02:23 - quantengine - INFO - 正在加载策略: MACrossStrategy
2025-05-21 23:02:23 - quantengine - ERROR - 策略加载失败: No module named 'quantengine.strategies.m_a_cross_strategy'
2025-05-21 23:05:48 - quantengine - INFO - 数据源加载完成
2025-05-21 23:05:48 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 23:05:48 - quantengine - ERROR - 策略加载失败: No module named 'quantengine.strategies.dual_ma'
2025-05-21 23:11:18 - quantengine - INFO - 数据源加载完成
2025-05-21 23:11:18 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 23:11:18 - quantengine - ERROR - 策略加载失败: module 'quantengine.strategies.dual_ma' has no attribute 'dual_ma_strategy'
2025-05-21 23:13:48 - quantengine - INFO - 数据源加载完成
2025-05-21 23:13:48 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-21 23:13:48 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-21 23:13:48 - quantengine - INFO - 正在创建输出目录: output
2025-05-21 23:13:48 - quantengine - INFO - 开始执行回测...
2025-05-21 23:13:48 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2023-12-31
2025-05-21 23:13:49 - quantengine - INFO - 数据获取完成，共970条记录
2025-05-21 23:13:49 - quantengine - INFO - 开始执行策略计算...
2025-05-21 23:13:49 - quantengine.strategy - INFO - Generated 468 entry signals and 483 exit signals
2025-05-21 23:13:49 - quantengine - INFO - 策略计算完成，生成468个入场信号和483个出场信号
2025-05-21 23:13:49 - quantengine - INFO - 开始执行回测计算...
2025-05-21 23:13:53 - quantengine - INFO - 生成了 24 条交易记录
2025-05-21 23:13:53 - quantengine - INFO - 回测完成，最终资产:106974.03
2025-05-21 23:13:53 - quantengine - INFO - 回测执行完成
2025-05-21 23:13:53 - quantengine - INFO - 开始生成报告...
2025-05-21 23:13:53 - quantengine.report - INFO - 开始生成回测报告...
2025-05-21 23:13:56 - quantengine.report - INFO - 将报告保存到 output
2025-05-21 23:13:56 - quantengine.report - INFO - 报告生成完成
2025-05-21 23:13:56 - quantengine - INFO - 报告生成完成，已保存至 output
