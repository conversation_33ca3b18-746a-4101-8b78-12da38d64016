import pandas as pd
import numpy as np
from .data_source import DataSource

class MockSource(DataSource):
    """模拟数据源，用于测试和开发"""
    
    def get_data(self, symbol, start_date, end_date, timeframe='1d'):
        """生成模拟数据"""
        # 创建日期范围
        start = pd.to_datetime(start_date)
        end = pd.to_datetime(end_date)
        
        # 根据时间周期生成不同的日期范围
        if timeframe == '1d':
            dates = pd.date_range(start=start, end=end, freq='B')  # 工作日
        elif timeframe == '1h':
            dates = pd.date_range(start=start, end=end, freq='H')
            # 只保留交易时间 (9:30-11:30, 13:00-15:00)
            trading_hours = ((dates.hour >= 9) & (dates.hour <= 11) | 
                            (dates.hour >= 13) & (dates.hour <= 15))
            dates = dates[trading_hours]
        elif timeframe == '1m':
            dates = pd.date_range(start=start, end=end, freq='min')
            # 只保留交易时间
            trading_hours = ((dates.hour >= 9) & (dates.hour <= 11) | 
                            (dates.hour >= 13) & (dates.hour <= 15))
            dates = dates[trading_hours]
        
        # 生成随机价格数据
        n = len(dates)
        np.random.seed(42)  # 固定随机种子，使结果可重现
        
        # 生成随机走势
        returns = np.random.normal(0.0005, 0.01, n)
        price = 100 * (1 + np.cumsum(returns))
        
        # 生成OHLCV数据
        data = {
            'open': price * np.random.uniform(0.99, 1.01, n),
            'high': price * np.random.uniform(1.01, 1.03, n),
            'low': price * np.random.uniform(0.97, 0.99, n),
            'close': price,
            'volume': np.random.randint(1000, 100000, n)
        }
        
        # 创建DataFrame
        df = pd.DataFrame(data, index=dates)
        
        # 确保high >= open, close, low和low <= open, close
        df['high'] = df[['high', 'open', 'close']].max(axis=1)
        df['low'] = df[['low', 'open', 'close']].min(axis=1)
        
        return df