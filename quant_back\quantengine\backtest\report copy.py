import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import vectorbt as vbt
import os
import json
import base64
import logging
from io import BytesIO
from datetime import datetime
import jinja2
import matplotlib.font_manager as fm
from matplotlib import rcParams

# 设置中文字体支持
def setup_chinese_font():
    """设置中文字体支持"""
    # 尝试设置中文字体
    try:
        # 检查系统中是否有中文字体
        chinese_fonts = [f for f in fm.findSystemFonts() if 'simhei' in f.lower() or 'msyh' in f.lower() or 'simsun' in f.lower()]
        if chinese_fonts:
            # 使用找到的第一个中文字体
            font_path = chinese_fonts[0]
            prop = fm.FontProperties(fname=font_path)
            plt.rcParams['font.family'] = prop.get_name()
        else:
            # 如果没有找到中文字体，使用系统默认字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS', 'sans-serif']
            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    except Exception as e:
        logging.warning(f"设置中文字体失败: {e}")
        # 使用备选方案
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS', 'sans-serif']
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 初始化中文字体
setup_chinese_font()

class BacktestReport:
    """回测报告生成器"""

    def __init__(self, portfolio, symbol, strategy_name):
        """
        初始化报告生成器

        参数:
            portfolio (vbt.Portfolio): 回测结果
            symbol (str): 证券代码
            strategy_name (str): 策略名称
        """
        self.portfolio = portfolio
        self.symbol = symbol
        self.strategy_name = strategy_name
        self.logger = logging.getLogger('quantengine.report')

    def generate_report(self, output_path=None, format='json'):
        """
        生成完整回测报告

        参数:
            output_path (str): 报告输出路径
            format (str): 报告格式，支持 'json', 'html', 'pdf'

        返回:
            dict: 包含报告数据的字典
        """
        self.logger.info("开始生成回测报告...")

        # 计算绩效指标
        self.logger.debug("计算绩效指标...")
        metrics = self._calculate_metrics()

        # 获取交易记录
        self.logger.debug("获取交易记录...")
        trades = self._get_trades()

        # 将交易记录中的时间戳列和持续时间列转换为字符串，以便JSON序列化
        if not trades.empty:
            for col in ['entry_time', 'exit_time']:
                if col in trades.columns:
                    trades[col] = trades[col].astype(str)
            if 'duration' in trades.columns:
                 # 将 Timedelta 转换为字符串
                 trades['duration'] = trades['duration'].astype(str)

        # 获取每日净值和收益率
        self.logger.debug("获取每日净值和收益率...")
        daily_returns = self._get_daily_returns()
        daily_values = self._get_daily_values()

        # 计算月度和年度收益
        self.logger.debug("计算月度和年度收益...")
        monthly_returns = self._get_monthly_returns()
        yearly_returns = self._get_yearly_returns()

        # 整合报告数据
        report = {
            'symbol': self.symbol,
            'strategy': self.strategy_name,
            'metrics': metrics,
            'trades': json.loads(trades.to_json(orient='records', date_format='iso')) if not trades.empty else [],
            'daily_returns': {str(k): v for k, v in daily_returns.to_dict().items()},
            'daily_values': {str(k): v for k, v in daily_values.to_dict().items()},
            'monthly_returns': {str(k): v for k, v in monthly_returns.to_dict().items()},
            'yearly_returns': {str(k): v for k, v in yearly_returns.to_dict().items()},
            'report_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # 转换非序列化对象
        report = self._convert_non_serializable(report)

        # 生成图表和报告
        if output_path:
            self.logger.info(f"将报告保存到 {output_path}")
            os.makedirs(output_path, exist_ok=True)

            # 根据格式生成不同类型的报告
            if format.lower() == 'json':
                # 保存JSON报告
                with open(os.path.join(output_path, 'report.json'), 'w', encoding='utf-8') as f:
                    json.dump(report, f, indent=2, ensure_ascii=False)
                self.logger.debug("JSON报告已保存")

            # 生成图表
            self.logger.info("生成图表...")
            self._generate_charts(output_path)
            self.logger.debug("图表生成完成")

            if format.lower() == 'html' or format.lower() == 'pdf':
                # 生成HTML报告
                self.logger.debug("生成HTML报告...")
                html_report = self._generate_html_report(report, output_path)

                # 保存HTML报告
                with open(os.path.join(output_path, 'report.html'), 'w', encoding='utf-8') as f:
                    f.write(html_report)
                self.logger.debug("HTML报告已保存")

                # 如果需要PDF报告，将HTML转换为PDF
                if format.lower() == 'pdf':
                    self.logger.debug("转换HTML为PDF...")
                    try:
                        self._convert_html_to_pdf(os.path.join(output_path, 'report.html'), os.path.join(output_path, 'report.pdf'))
                        self.logger.debug("PDF报告已生成")
                    except Exception as e:
                        self.logger.error(f"生成PDF报告失败: {str(e)}")

        self.logger.info("报告生成完成")
        return report


    def _convert_non_serializable(self, obj):
        """递归转换非 JSON 序列化对象"""
        if isinstance(obj, (pd.Timestamp, pd.Timedelta)):
            return str(obj)
        elif isinstance(obj, dict):
            # Convert both keys and values
            return {str(k) if isinstance(k, (pd.Timestamp, pd.Timedelta)) else k: self._convert_non_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_non_serializable(item) for item in obj]
        elif isinstance(obj, pd.Series):
            # Convert Series to dictionary and recursively process values
            return {str(k): self._convert_non_serializable(v) for k, v in obj.to_dict().items()}
        elif isinstance(obj, pd.DataFrame):
            # Convert DataFrame to list of dictionaries (orient='records')
            return json.loads(obj.to_json(orient='records', date_format='iso'))
        else:
            return obj

    def _calculate_metrics(self):
        """计算绩效指标"""
        stats = self.portfolio.stats()

        # 基础指标
        metrics = {
            # 收益相关指标
            'total_return': stats.get('total_return', 0.0),  # 总收益率，添加容错处理
            'annual_return': stats.get('annual_return', 0.0),  # 年化收益率，添加容错处理
            'daily_return': stats.get('daily_return', 0.0),  # 日均收益率，添加容错处理
            'max_drawdown': stats.get('max_drawdown', 0.0),  # 最大回撤，添加容错处理
            'max_drawdown_duration': stats.get('max_drawdown_duration', 0),  # 最大回撤持续时间，添加容错处理

            # 风险调整后收益指标
            'sharpe_ratio': stats.get('sharpe_ratio', 0.0),  # 夏普比率，添加容错处理
            'sortino_ratio': stats.get('sortino_ratio', 0.0),  # 索提诺比率，添加容错处理
            'calmar_ratio': stats.get('calmar_ratio', 0.0),  # 卡玛比率，添加容错处理
            'omega_ratio': stats.get('omega_ratio', 0.0),  # 欧米伽比率，添加容错处理
            'volatility_annual': stats.get('volatility_annual', 0.0),  # 年化波动率，添加容错处理

            # 交易相关指标
            'win_rate': stats.get('win_rate', 0.0),  # 胜率，添加容错处理
            'profit_factor': stats.get('profit_factor', 0.0),  # 盈亏比，添加容错处理
            'expectancy': stats.get('expectancy', 0.0),  # 期望值，添加容错处理
            'avg_win': stats.get('avg_win', 0.0),  # 平均盈利，添加容错处理
            'avg_loss': stats.get('avg_loss', 0.0),  # 平均亏损，添加容错处理
            'best_trade': stats.get('best_trade', 0.0),  # 最佳交易，添加容错处理
            'worst_trade': stats.get('worst_trade', 0.0),  # 最差交易，添加容错处理
            'avg_trade': stats.get('avg_trade', 0.0),  # 平均交易收益，添加容错处理
            'num_trades': stats.get('num_trades', 0),  # 交易次数，添加容错处理
            'max_trade_duration': stats.get('max_trade_duration', 0),  # 最长交易持续时间，添加容错处理
            'avg_trade_duration': stats.get('avg_trade_duration', 0),  # 平均交易持续时间，添加容错处理
        }

        # 添加额外计算的指标
        if self.portfolio.trades.count() > 0:
            trades = self.portfolio.trades.records_readable

            # 计算连续盈利和连续亏损的最大次数
            if 'return' in trades.columns:
                returns = trades['return'].values
                win_streak = 0
                loss_streak = 0
                max_win_streak = 0
                max_loss_streak = 0

                for ret in returns:
                    if ret > 0:
                        win_streak += 1
                        loss_streak = 0
                        max_win_streak = max(max_win_streak, win_streak)
                    elif ret < 0:
                        loss_streak += 1
                        win_streak = 0
                        max_loss_streak = max(max_loss_streak, loss_streak)
                    else:
                        win_streak = 0
                        loss_streak = 0

                metrics['max_win_streak'] = max_win_streak  # 最大连续盈利次数
                metrics['max_loss_streak'] = max_loss_streak  # 最大连续亏损次数

            # 计算平均持仓时间
            if 'duration' in trades.columns:
                metrics['avg_holding_period'] = trades['duration'].mean()  # 平均持仓时间

        # 计算年化收益与波动率比
        if 'volatility_annual' in stats and stats['volatility_annual'] != 0:
            metrics['return_volatility_ratio'] = stats['annual_return'] / stats['volatility_annual']

        # 计算最大单日收益和最大单日亏损
        daily_returns = self.portfolio.returns()
        if not daily_returns.empty:
            metrics['max_daily_profit'] = daily_returns.max()  # 最大单日收益
            metrics['max_daily_loss'] = daily_returns.min()  # 最大单日亏损

        # 将 Timedelta 和 Timestamp 对象转换为字符串，以便JSON序列化
        for key, value in metrics.items():
            if isinstance(value, (pd.Timedelta, pd.Timestamp)):
                metrics[key] = str(value)

        return metrics

    def _get_trades(self):
        """获取交易记录"""
        if self.portfolio.trades.count() == 0:
            return pd.DataFrame()
        return self.portfolio.trades.records_readable

    def _get_daily_returns(self):
        """获取每日收益率"""
        return self.portfolio.returns().to_frame('daily_return')

    def _get_daily_values(self):
        """获取每日净值"""
        return self.portfolio.value().to_frame('value')

    def _get_monthly_returns(self):
        """获取月度收益率"""
        daily_returns = self.portfolio.returns()
        return daily_returns.resample('M').apply(lambda x: (1 + x).prod() - 1).to_frame('monthly_return')

    def _get_yearly_returns(self):
        """获取年度收益率"""
        daily_returns = self.portfolio.returns()
        return daily_returns.resample('Y').apply(lambda x: (1 + x).prod() - 1).to_frame('yearly_return')

    def _plot_chart(self, x=None, y=None, data=None, chart_type='line', title='', xlabel='', ylabel='',
                  filename='chart.png', output_path='.', figsize=(12, 6), **kwargs):
        """
        通用图表绘制函数

        参数:
            x (str): x轴数据列名
            y (str): y轴数据列名
            data (pd.DataFrame): 数据
            chart_type (str): 图表类型，支持 'line', 'scatter', 'hist', 'heatmap', 'bar', 'box', 'violin', 'area', 'pie', 'portfolio', 'drawdown', 'underwater'
            title (str): 图表标题
            xlabel (str): x轴标签
            ylabel (str): y轴标签
            filename (str): 输出文件名
            output_path (str): 输出路径
            figsize (tuple): 图表大小
            **kwargs: 其他参数传递给绘图函数
        """
        # 设置非交互式后端，确保在无图形界面环境下也能生成图表
        plt.switch_backend('Agg')

        fig, ax = plt.subplots(figsize=figsize)

        if chart_type == 'line':
            if data is not None:
                data.plot(ax=ax, **kwargs)
            else:
                ax.plot(x, y, **kwargs)
        elif chart_type == 'scatter':
            sns.scatterplot(x=x, y=y, data=data, ax=ax, **kwargs)
        elif chart_type == 'hist':
            sns.histplot(x=x, data=data, ax=ax, **kwargs)
        elif chart_type == 'heatmap':
            sns.heatmap(data, ax=ax, **kwargs)
        elif chart_type == 'bar':
            if data is not None:
                if x is not None and y is not None:
                    sns.barplot(x=x, y=y, data=data, ax=ax, **kwargs)
                else:
                    data.plot(kind='bar', ax=ax, **kwargs)
            else:
                ax.bar(x, y, **kwargs)
        elif chart_type == 'box':
            sns.boxplot(x=x, y=y, data=data, ax=ax, **kwargs)
        elif chart_type == 'violin':
            sns.violinplot(x=x, y=y, data=data, ax=ax, **kwargs)
        elif chart_type == 'area':
            if data is not None:
                data.plot(kind='area', ax=ax, stacked=kwargs.pop('stacked', True), **kwargs)
            else:
                ax.fill_between(x, y, **kwargs)
        elif chart_type == 'pie':
            if data is not None:
                data.plot(kind='pie', ax=ax, **kwargs)
            else:
                ax.pie(y, labels=x, **kwargs)
        elif chart_type == 'portfolio':
            # 使用plotly原生方式绘制组合净值曲线
            print(f"DEBUG: Plotting portfolio chart for {self.symbol}...")
            print("DEBUG: Before self.portfolio.plot...") # Added log
            try:
                # 尝试使用vectorbt/plotly方式绘制
                fig = self.portfolio.plot(**kwargs)
                print("DEBUG: After self.portfolio.plot.") # Added log
                print("DEBUG: Portfolio chart plotted.")
                print(f"DEBUG: Attempting to save chart to {os.path.join(output_path, filename)}...")

                try:
                    print("DEBUG: Before fig.to_image...")
                    try:
                        # 尝试使用kaleido将plotly图表转换为图片
                        img_bytes = fig.to_image(format='png') # Specify format as png
                        print("DEBUG: After fig.to_image.")
                        print("DEBUG: Before writing file...")

                        with open(os.path.join(output_path, filename), 'wb') as f:
                            f.write(img_bytes)
                        print("DEBUG: After writing file.")
                        print(f"DEBUG: Successfully saved chart to {os.path.join(output_path, filename)}.")
                        return
                    except Exception as e:
                        print(f"DEBUG: Error during fig.to_image: {e}")
                        print("DEBUG: Falling back to matplotlib for portfolio chart...")
                except Exception as e:
                    print(f"DEBUG: Error saving chart to {os.path.join(output_path, filename)}: {e}")
                    print("DEBUG: Falling back to matplotlib for portfolio chart...")
            except Exception as e:
                print(f"DEBUG: Error creating plotly chart: {e}")
                print("DEBUG: Falling back to matplotlib for portfolio chart...")

            # 如果plotly方式失败，使用matplotlib作为备选方案
            try:
                print("DEBUG: Using matplotlib fallback for portfolio chart...")
                # 获取净值数据
                equity = self.portfolio.value()

                # 创建matplotlib图表
                plt.figure(figsize=figsize)
                equity.plot(title=f'{self.symbol} - {self.strategy_name} 净值曲线')
                plt.grid(True)
                plt.tight_layout()

                # 保存图表
                plt.savefig(os.path.join(output_path, filename))
                plt.close()
                print(f"DEBUG: Successfully saved matplotlib chart to {os.path.join(output_path, filename)}.")
            except Exception as e:
                print(f"DEBUG: Matplotlib fallback also failed: {e}")
                import traceback
                traceback.print_exc()

            return
        elif chart_type == 'drawdown':
            # 使用plotly原生方式绘制回撤图
            print("DEBUG: Plotting drawdown chart...")
            try:
                # 尝试使用vectorbt/plotly方式绘制
                fig = self.portfolio.plot_drawdowns(**kwargs)
                try:
                    print(f"DEBUG: Attempting to save chart to {os.path.join(output_path, filename)}...")
                    img_bytes = fig.to_image(format='png') # Specify format as png
                    with open(os.path.join(output_path, filename), 'wb') as f:
                        f.write(img_bytes)
                    print(f"DEBUG: Successfully saved chart to {os.path.join(output_path, filename)}.")
                    return
                except Exception as e:
                    print(f"DEBUG: Error saving drawdown chart: {e}")
                    print("DEBUG: Falling back to matplotlib for drawdown chart...")
            except Exception as e:
                print(f"DEBUG: Error creating plotly drawdown chart: {e}")
                print("DEBUG: Falling back to matplotlib for drawdown chart...")

            # 如果plotly方式失败，使用matplotlib作为备选方案
            try:
                print("DEBUG: Using matplotlib fallback for drawdown chart...")
                # 获取回撤数据
                drawdowns = self.portfolio.drawdown()

                # 创建matplotlib图表
                plt.figure(figsize=figsize)
                drawdowns.plot(title=f'{self.symbol} - {self.strategy_name} 回撤分析')
                plt.grid(True)
                plt.tight_layout()

                # 保存图表
                plt.savefig(os.path.join(output_path, filename))
                plt.close()
                print(f"DEBUG: Successfully saved matplotlib drawdown chart to {os.path.join(output_path, filename)}.")
            except Exception as e:
                print(f"DEBUG: Matplotlib fallback for drawdown also failed: {e}")
                import traceback
                traceback.print_exc()

            return
        elif chart_type == 'underwater':
            # 使用plotly原生方式绘制水下图
            print("DEBUG: Plotting underwater chart...")
            try:
                # 尝试使用vectorbt/plotly方式绘制
                drawdown = self.portfolio.drawdown()
                fig = drawdown.plot(**kwargs)
                try:
                    print(f"DEBUG: Attempting to save chart to {os.path.join(output_path, filename)}...")
                    img_bytes = fig.to_image(format='png') # Specify format as png
                    with open(os.path.join(output_path, filename), 'wb') as f:
                        f.write(img_bytes)
                    print(f"DEBUG: Successfully saved chart to {os.path.join(output_path, filename)}.")
                    return
                except Exception as e:
                    print(f"DEBUG: Error saving underwater chart: {e}")
                    print("DEBUG: Falling back to matplotlib for underwater chart...")
            except Exception as e:
                print(f"DEBUG: Error creating plotly underwater chart: {e}")
                print("DEBUG: Falling back to matplotlib for underwater chart...")

            # 如果plotly方式失败，使用matplotlib作为备选方案
            try:
                print("DEBUG: Using matplotlib fallback for underwater chart...")
                # 获取回撤数据
                drawdown = self.portfolio.drawdown()

                # 创建matplotlib图表
                plt.figure(figsize=figsize)
                drawdown.plot(title=f'{self.symbol} - {self.strategy_name} 水下图')
                plt.grid(True)
                plt.tight_layout()

                # 保存图表
                plt.savefig(os.path.join(output_path, filename))
                plt.close()
                print(f"DEBUG: Successfully saved matplotlib underwater chart to {os.path.join(output_path, filename)}.")
            except Exception as e:
                print(f"DEBUG: Matplotlib fallback for underwater also failed: {e}")
                import traceback
                traceback.print_exc()

            return
        elif chart_type == 'rolling_sharpe':
            # 绘制滚动夏普比率
            window = kwargs.pop('window', 252)  # 默认使用252个交易日(一年)
            returns = self.portfolio.returns()
            rolling_sharpe = returns.rolling(window=window).apply(
                lambda x: np.sqrt(window) * x.mean() / x.std() if x.std() != 0 else 0
            )
            rolling_sharpe.plot(ax=ax, **kwargs)
        elif chart_type == 'rolling_volatility':
            # 绘制滚动波动率
            window = kwargs.pop('window', 252)  # 默认使用252个交易日(一年)
            returns = self.portfolio.returns()
            rolling_vol = returns.rolling(window=window).std() * np.sqrt(252)
            rolling_vol.plot(ax=ax, **kwargs)
        elif chart_type == 'cumulative_returns':
            # 绘制累积收益率
            returns = self.portfolio.returns()
            cumulative = (1 + returns).cumprod() - 1
            cumulative.plot(ax=ax, **kwargs)
        elif chart_type == 'monthly_heatmap':
            # 月度收益热力图
            returns = self.portfolio.returns()
            monthly_returns = returns.groupby([returns.index.year, returns.index.month]).apply(
                lambda x: (1 + x).prod() - 1
            ).unstack()
            sns.heatmap(monthly_returns, ax=ax, annot=True, fmt=".2%", cmap="RdYlGn", center=0, **kwargs)

        plt.title(title)
        plt.xlabel(xlabel)
        plt.ylabel(ylabel)
        plt.tight_layout()
        plt.savefig(os.path.join(output_path, filename), dpi=300, bbox_inches='tight')
        plt.close(fig)

    def _generate_charts(self, output_path):
        """生成图表"""
        os.makedirs(output_path, exist_ok=True)

        # 设置图表风格
        plt.style.use('seaborn-v0_8-darkgrid')

        # 设置图表全局参数
        plt.rcParams['figure.figsize'] = (12, 6)
        plt.rcParams['figure.dpi'] = 100
        plt.rcParams['savefig.dpi'] = 300
        plt.rcParams['savefig.bbox'] = 'tight'
        plt.rcParams['savefig.pad_inches'] = 0.1

        # 设置网格样式
        plt.rcParams['grid.alpha'] = 0.3
        plt.rcParams['grid.linestyle'] = '--'

        # 基础绩效图表
        self.logger.debug("生成基础绩效图表...")
        self._generate_performance_charts(output_path)

        # 交易分析图表
        if self.portfolio.trades.count() > 0:
            self.logger.debug("生成交易分析图表...")
            self._generate_trade_charts(output_path)
        else:
            self.logger.debug("没有交易记录，跳过交易图表生成")

        # 风险分析图表
        self.logger.debug("生成风险分析图表...")
        self._generate_risk_charts(output_path)

        # 高级分析图表
        self.logger.debug("生成高级分析图表...")
        self._generate_advanced_charts(output_path)

    def _generate_performance_charts(self, output_path):
        """生成基础绩效图表"""

        # 1. 绘制净值曲线
        try:
            fig, ax = plt.subplots(figsize=(12, 6))
            equity = self.portfolio.value()
            equity.plot(ax=ax, color='#1f77b4', linewidth=2)

            # 添加标题和标签
            ax.set_title(f'{self.symbol} - {self.strategy_name} 净值曲线', fontsize=14, fontweight='bold')
            ax.set_xlabel('日期', fontsize=12)
            ax.set_ylabel('净值', fontsize=12)

            # 添加网格线
            ax.grid(True, linestyle='--', alpha=0.7)

            # 添加起始和结束标记
            ax.scatter(equity.index[0], equity.iloc[0], color='green', s=100, zorder=5, label='开始')
            ax.scatter(equity.index[-1], equity.iloc[-1], color='red', s=100, zorder=5, label='结束')

            # 添加图例
            ax.legend(loc='best')

            # 格式化y轴
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x:.2f}'))

            plt.tight_layout()
            plt.savefig(os.path.join(output_path, 'equity_curve.png'), dpi=300, bbox_inches='tight')
            plt.close(fig)
            self.logger.debug("净值曲线绘制完成")
        except Exception as e:
            self.logger.error(f"绘制净值曲线失败: {e}")

        # 2. 绘制累积收益率曲线
        try:
            fig, ax = plt.subplots(figsize=(12, 6))
            returns = self.portfolio.returns()
            cumulative = (1 + returns).cumprod() - 1
            cumulative.plot(ax=ax, color='#2ca02c', linewidth=2)

            # 添加标题和标签
            ax.set_title(f'{self.symbol} - {self.strategy_name} 累积收益率', fontsize=14, fontweight='bold')
            ax.set_xlabel('日期', fontsize=12)
            ax.set_ylabel('累积收益率', fontsize=12)

            # 添加网格线
            ax.grid(True, linestyle='--', alpha=0.7)

            # 添加零线
            ax.axhline(y=0, color='r', linestyle='-', alpha=0.3)

            # 格式化y轴为百分比
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x*100:.1f}%'))

            plt.tight_layout()
            plt.savefig(os.path.join(output_path, 'cumulative_returns.png'), dpi=300, bbox_inches='tight')
            plt.close(fig)
            self.logger.debug("累积收益率曲线绘制完成")
        except Exception as e:
            self.logger.error(f"绘制累积收益率曲线失败: {e}")

        # 3. 绘制月度收益热力图
        try:
            fig, ax = plt.subplots(figsize=(14, 8))
            returns = self.portfolio.returns()

            # 创建月度收益表
            monthly_returns = returns.groupby([returns.index.year, returns.index.month]).apply(
                lambda x: (1 + x).prod() - 1
            ).unstack()

            # 使用更好的颜色映射
            cmap = sns.diverging_palette(10, 133, as_cmap=True)

            # 绘制热力图
            sns.heatmap(monthly_returns, annot=True, fmt=".2%", cmap=cmap, center=0,
                        linewidths=.5, cbar_kws={"shrink": .8}, ax=ax)

            # 设置标题和标签
            ax.set_title(f'{self.symbol} - {self.strategy_name} 月度收益热力图', fontsize=14, fontweight='bold')
            ax.set_xlabel('月份', fontsize=12)
            ax.set_ylabel('年份', fontsize=12)

            # 调整坐标轴标签
            ax.set_xticklabels(['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'], rotation=0)

            plt.tight_layout()
            plt.savefig(os.path.join(output_path, 'monthly_returns_heatmap.png'), dpi=300, bbox_inches='tight')
            plt.close(fig)
            self.logger.debug("月度收益热力图绘制完成")
        except Exception as e:
            self.logger.error(f"绘制月度收益热力图失败: {e}")

        # 4. 绘制年度收益柱状图
        yearly_returns = self._get_yearly_returns()
        if not yearly_returns.empty:
            try:
                fig, ax = plt.subplots(figsize=(12, 6))

                # 提取年份和收益率
                years = [str(idx.year) for idx in yearly_returns.index]
                values = yearly_returns['yearly_return'].values

                # 设置柱状图颜色
                colors = ['#2ca02c' if x > 0 else '#d62728' for x in values]

                # 绘制柱状图
                bars = ax.bar(years, values, color=colors, alpha=0.7)

                # 添加数值标签
                for bar in bars:
                    height = bar.get_height()
                    ax.annotate(f'{height*100:.1f}%',
                                xy=(bar.get_x() + bar.get_width() / 2, height),
                                xytext=(0, 3),  # 3点垂直偏移
                                textcoords="offset points",
                                ha='center', va='bottom',
                                fontweight='bold')

                # 添加标题和标签
                ax.set_title(f'{self.symbol} - {self.strategy_name} 年度收益', fontsize=14, fontweight='bold')
                ax.set_xlabel('年份', fontsize=12)
                ax.set_ylabel('收益率', fontsize=12)

                # 添加网格线
                ax.grid(True, linestyle='--', alpha=0.3, axis='y')

                # 添加零线
                ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)

                # 格式化y轴为百分比
                ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x*100:.1f}%'))

                plt.tight_layout()
                plt.savefig(os.path.join(output_path, 'yearly_returns.png'), dpi=300, bbox_inches='tight')
                plt.close(fig)
                self.logger.debug("年度收益柱状图绘制完成")
            except Exception as e:
                self.logger.error(f"绘制年度收益柱状图失败: {e}")
        else:
            self.logger.debug("没有年度收益数据，跳过年度收益柱状图绘制")

        print("DEBUG: Plotting monthly returns bar chart...") # Added log
        # 5. 绘制月度收益柱状图
        monthly_returns = self._get_monthly_returns()
        if not monthly_returns.empty:
            self._plot_chart(
                data=monthly_returns,
                chart_type='bar',
                title=f'{self.symbol} - {self.strategy_name} 月度收益',
                xlabel='月份',
                ylabel='收益率',
                filename='monthly_returns.png',
                output_path=output_path,
                figsize=(14, 6)
            )
        print("DEBUG: Monthly returns bar chart plotted.") # Added log

    def _generate_risk_charts(self, output_path):
        """生成风险分析图表"""

        # 1. 绘制回撤曲线
        try:
            fig, ax = plt.subplots(figsize=(12, 6))
            drawdowns = self.portfolio.drawdown()
            drawdowns.plot(ax=ax, color='#e74c3c', linewidth=2)

            # 添加标题和标签
            ax.set_title(f'{self.symbol} - {self.strategy_name} 回撤分析', fontsize=14, fontweight='bold')
            ax.set_xlabel('日期', fontsize=12)
            ax.set_ylabel('回撤', fontsize=12)

            # 添加网格线
            ax.grid(True, linestyle='--', alpha=0.7)

            # 格式化y轴为百分比
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x*100:.1f}%'))

            plt.tight_layout()
            plt.savefig(os.path.join(output_path, 'drawdowns.png'), dpi=300, bbox_inches='tight')
            plt.close(fig)
            self.logger.debug("回撤曲线绘制完成")
        except Exception as e:
            self.logger.error(f"绘制回撤曲线失败: {e}")

        # 2. 绘制水下图 (与回撤曲线类似，但可能有不同的表现形式)
        try:
            fig, ax = plt.subplots(figsize=(12, 6))
            drawdowns = self.portfolio.drawdown()
            drawdowns.plot(ax=ax, color='#3498db', linewidth=2, alpha=0.7)

            # 添加标题和标签
            ax.set_title(f'{self.symbol} - {self.strategy_name} 水下图', fontsize=14, fontweight='bold')
            ax.set_xlabel('日期', fontsize=12)
            ax.set_ylabel('回撤', fontsize=12)

            # 添加网格线
            ax.grid(True, linestyle='--', alpha=0.7)

            # 填充区域
            ax.fill_between(drawdowns.index, 0, drawdowns.values, color='#3498db', alpha=0.3)

            # 格式化y轴为百分比
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x*100:.1f}%'))

            plt.tight_layout()
            plt.savefig(os.path.join(output_path, 'underwater.png'), dpi=300, bbox_inches='tight')
            plt.close(fig)
            self.logger.debug("水下图绘制完成")
        except Exception as e:
            self.logger.error(f"绘制水下图失败: {e}")

        # 3. 绘制滚动夏普比率
        try:
            fig, ax = plt.subplots(figsize=(12, 6))
            window = 252  # 默认使用252个交易日(一年)
            returns = self.portfolio.returns()
            rolling_sharpe = returns.rolling(window=window).apply(
                lambda x: np.sqrt(window) * x.mean() / x.std() if x.std() != 0 else 0
            )

            # 绘制滚动夏普比率
            rolling_sharpe.plot(ax=ax, color='#9b59b6', linewidth=2)

            # 添加标题和标签
            ax.set_title(f'{self.symbol} - {self.strategy_name} 滚动夏普比率 (252天)', fontsize=14, fontweight='bold')
            ax.set_xlabel('日期', fontsize=12)
            ax.set_ylabel('夏普比率', fontsize=12)

            # 添加网格线
            ax.grid(True, linestyle='--', alpha=0.7)

            # 添加零线
            ax.axhline(y=0, color='r', linestyle='-', alpha=0.3)

            # 添加参考线
            ax.axhline(y=1, color='orange', linestyle='--', alpha=0.5, label='夏普=1')
            ax.axhline(y=2, color='green', linestyle='--', alpha=0.5, label='夏普=2')
            ax.legend(loc='best')

            plt.tight_layout()
            plt.savefig(os.path.join(output_path, 'rolling_sharpe.png'), dpi=300, bbox_inches='tight')
            plt.close(fig)
            self.logger.debug("滚动夏普比率绘制完成")
        except Exception as e:
            self.logger.error(f"绘制滚动夏普比率失败: {e}")

        # 4. 绘制滚动波动率
        try:
            fig, ax = plt.subplots(figsize=(12, 6))
            window = 252  # 默认使用252个交易日(一年)
            returns = self.portfolio.returns()
            rolling_vol = returns.rolling(window=window).std() * np.sqrt(252)

            # 绘制滚动波动率
            rolling_vol.plot(ax=ax, color='#f39c12', linewidth=2)

            # 添加标题和标签
            ax.set_title(f'{self.symbol} - {self.strategy_name} 滚动波动率 (252天)', fontsize=14, fontweight='bold')
            ax.set_xlabel('日期', fontsize=12)
            ax.set_ylabel('波动率', fontsize=12)

            # 添加网格线
            ax.grid(True, linestyle='--', alpha=0.7)

            # 格式化y轴为百分比
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x*100:.1f}%'))

            plt.tight_layout()
            plt.savefig(os.path.join(output_path, 'rolling_volatility.png'), dpi=300, bbox_inches='tight')
            plt.close(fig)
            self.logger.debug("滚动波动率绘制完成")
        except Exception as e:
            self.logger.error(f"绘制滚动波动率失败: {e}")


    def _generate_trade_charts(self, output_path):
        """生成交易分析图表"""
        trades = self._get_trades()
        if trades.empty:
            self.logger.debug("没有交易记录，跳过交易图表生成")
            return

        # 检查是否有 'return' 列，如果没有但有 'pnl' 或 'pnl_pct'，则创建
        if 'return' not in trades.columns:
            if 'pnl_pct' in trades.columns:
                trades['return'] = trades['pnl_pct']
                self.logger.debug("从 'pnl_pct' 创建 'return' 列")
            elif 'pnl' in trades.columns and 'size' in trades.columns and 'entry_price' in trades.columns:
                # 计算百分比收益
                trades['return'] = trades['pnl'] / (trades['size'] * trades['entry_price'])
                self.logger.debug("从 'pnl' 和 'size' 创建 'return' 列")
            else:
                self.logger.warning("无法创建 'return' 列，跳过基于收益率的图表")

        # 1. 交易收益分布直方图
        if 'return' in trades.columns:
            try:
                fig, ax = plt.subplots(figsize=(12, 6))

                # 计算正负收益的数量
                positive_returns = (trades['return'] > 0).sum()
                negative_returns = (trades['return'] < 0).sum()
                win_rate = positive_returns / len(trades) if len(trades) > 0 else 0

                # 绘制直方图
                n, bins, patches = ax.hist(trades['return'], bins=50, alpha=0.7, color='#3498db')

                # 为正负收益设置不同颜色
                for i in range(len(patches)):
                    if bins[i] < 0:
                        patches[i].set_facecolor('#e74c3c')  # 负收益为红色
                    else:
                        patches[i].set_facecolor('#2ecc71')  # 正收益为绿色

                # 添加标题和标签
                ax.set_title(f'{self.symbol} - {self.strategy_name} 交易收益分布', fontsize=14, fontweight='bold')
                ax.set_xlabel('收益率', fontsize=12)
                ax.set_ylabel('频数', fontsize=12)

                # 添加胜率信息
                ax.text(0.02, 0.95, f'总交易次数: {len(trades)}', transform=ax.transAxes, fontsize=10)
                ax.text(0.02, 0.90, f'盈利交易: {positive_returns}', transform=ax.transAxes, fontsize=10, color='#2ecc71')
                ax.text(0.02, 0.85, f'亏损交易: {negative_returns}', transform=ax.transAxes, fontsize=10, color='#e74c3c')
                ax.text(0.02, 0.80, f'胜率: {win_rate:.2%}', transform=ax.transAxes, fontsize=10, fontweight='bold')

                # 添加网格线
                ax.grid(True, linestyle='--', alpha=0.7)

                # 格式化x轴为百分比
                ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x*100:.1f}%'))

                plt.tight_layout()
                plt.savefig(os.path.join(output_path, 'trade_returns_hist.png'), dpi=300, bbox_inches='tight')
                plt.close(fig)
                self.logger.debug("交易收益分布直方图绘制完成")
            except Exception as e:
                self.logger.error(f"绘制交易收益分布直方图失败: {e}")

        # 2. 交易持续时间分布直方图
        if 'duration' in trades.columns:
            try:
                fig, ax = plt.subplots(figsize=(12, 6))

                # 将 Timedelta 转换为总秒数或天数进行绘制
                trades['duration_seconds'] = trades['duration'].apply(lambda x: x.total_seconds() if isinstance(x, pd.Timedelta) else 0)

                # 将秒转换为天
                trades['duration_days'] = trades['duration_seconds'] / (24 * 60 * 60)

                # 绘制直方图
                ax.hist(trades['duration_days'], bins=50, alpha=0.7, color='#9b59b6')

                # 添加标题和标签
                ax.set_title(f'{self.symbol} - {self.strategy_name} 交易持续时间分布', fontsize=14, fontweight='bold')
                ax.set_xlabel('持续时间 (天)', fontsize=12)
                ax.set_ylabel('频数', fontsize=12)

                # 添加统计信息
                avg_duration = trades['duration_days'].mean()
                max_duration = trades['duration_days'].max()
                min_duration = trades['duration_days'].min()

                ax.text(0.02, 0.95, f'平均持仓时间: {avg_duration:.2f} 天', transform=ax.transAxes, fontsize=10)
                ax.text(0.02, 0.90, f'最长持仓时间: {max_duration:.2f} 天', transform=ax.transAxes, fontsize=10)
                ax.text(0.02, 0.85, f'最短持仓时间: {min_duration:.2f} 天', transform=ax.transAxes, fontsize=10)

                # 添加网格线
                ax.grid(True, linestyle='--', alpha=0.7)

                plt.tight_layout()
                plt.savefig(os.path.join(output_path, 'trade_duration_hist.png'), dpi=300, bbox_inches='tight')
                plt.close(fig)
                self.logger.debug("交易持续时间分布直方图绘制完成")
            except Exception as e:
                self.logger.error(f"绘制交易持续时间分布直方图失败: {e}")

        # 3. 盈利/亏损交易收益箱线图
        if 'return' in trades.columns:
            try:
                trades['trade_type'] = trades['return'].apply(lambda x: '盈利' if x > 0 else ('亏损' if x < 0 else '持平'))
                if not trades[trades['trade_type'] != '持平'].empty:
                    fig, ax = plt.subplots(figsize=(10, 6))

                    # 使用更好的颜色
                    palette = {'盈利': '#2ecc71', '亏损': '#e74c3c'}

                    # 绘制箱线图
                    sns.boxplot(x='trade_type', y='return', data=trades[trades['trade_type'] != '持平'],
                                palette=palette, ax=ax)

                    # 添加标题和标签
                    ax.set_title(f'{self.symbol} - {self.strategy_name} 盈利/亏损交易收益', fontsize=14, fontweight='bold')
                    ax.set_xlabel('交易类型', fontsize=12)
                    ax.set_ylabel('收益率', fontsize=12)

                    # 添加网格线
                    ax.grid(True, linestyle='--', alpha=0.7, axis='y')

                    # 格式化y轴为百分比
                    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x*100:.1f}%'))

                    # 添加统计信息
                    win_returns = trades[trades['trade_type'] == '盈利']['return']
                    loss_returns = trades[trades['trade_type'] == '亏损']['return']

                    if not win_returns.empty and not loss_returns.empty:
                        avg_win = win_returns.mean()
                        avg_loss = loss_returns.mean()
                        profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')

                        ax.text(0.02, 0.95, f'平均盈利: {avg_win*100:.2f}%', transform=ax.transAxes, fontsize=10, color='#2ecc71')
                        ax.text(0.02, 0.90, f'平均亏损: {avg_loss*100:.2f}%', transform=ax.transAxes, fontsize=10, color='#e74c3c')
                        ax.text(0.02, 0.85, f'盈亏比: {profit_factor:.2f}', transform=ax.transAxes, fontsize=10, fontweight='bold')

                    plt.tight_layout()
                    plt.savefig(os.path.join(output_path, 'win_loss_trade_returns_box.png'), dpi=300, bbox_inches='tight')
                    plt.close(fig)
                    self.logger.debug("盈利/亏损交易收益箱线图绘制完成")
            except Exception as e:
                self.logger.error(f"绘制盈利/亏损交易收益箱线图失败: {e}")

        # 4. 交易入场/出场价格散点图
        if 'entry_price' in trades.columns and 'exit_price' in trades.columns and 'entry_time' in trades.columns and 'exit_time' in trades.columns:
            try:
                # 创建一个包含价格和时间的图表
                fig, ax = plt.subplots(figsize=(14, 8))

                # 将时间列转换为datetime对象
                if isinstance(trades['entry_time'].iloc[0], str):
                    trades['entry_time'] = pd.to_datetime(trades['entry_time'])
                if isinstance(trades['exit_time'].iloc[0], str):
                    trades['exit_time'] = pd.to_datetime(trades['exit_time'])

                # 绘制入场和出场价格
                for i, trade in trades.iterrows():
                    # 根据交易结果设置颜色
                    if 'return' in trades.columns:
                        color = '#2ecc71' if trade['return'] > 0 else '#e74c3c'
                    else:
                        color = '#3498db'

                    # 绘制入场点
                    ax.scatter(trade['entry_time'], trade['entry_price'], color=color, marker='^', s=100, alpha=0.7)

                    # 绘制出场点
                    ax.scatter(trade['exit_time'], trade['exit_price'], color=color, marker='v', s=100, alpha=0.7)

                    # 连接入场和出场点
                    ax.plot([trade['entry_time'], trade['exit_time']],
                            [trade['entry_price'], trade['exit_price']],
                            color=color, alpha=0.5, linestyle='--')

                # 添加标题和标签
                ax.set_title(f'{self.symbol} - {self.strategy_name} 交易入场/出场价格', fontsize=14, fontweight='bold')
                ax.set_xlabel('时间', fontsize=12)
                ax.set_ylabel('价格', fontsize=12)

                # 添加图例
                from matplotlib.lines import Line2D
                legend_elements = [
                    Line2D([0], [0], marker='^', color='w', markerfacecolor='gray', markersize=10, label='入场点'),
                    Line2D([0], [0], marker='v', color='w', markerfacecolor='gray', markersize=10, label='出场点'),
                ]
                if 'return' in trades.columns:
                    legend_elements.extend([
                        Line2D([0], [0], color='#2ecc71', lw=2, label='盈利交易'),
                        Line2D([0], [0], color='#e74c3c', lw=2, label='亏损交易'),
                    ])
                ax.legend(handles=legend_elements, loc='best')

                # 添加网格线
                ax.grid(True, linestyle='--', alpha=0.7)

                # 格式化x轴日期
                fig.autofmt_xdate()

                plt.tight_layout()
                plt.savefig(os.path.join(output_path, 'trade_price_chart.png'), dpi=300, bbox_inches='tight')
                plt.close(fig)
                self.logger.debug("交易入场/出场价格图绘制完成")
            except Exception as e:
                self.logger.error(f"绘制交易入场/出场价格图失败: {e}")
        else:
            self.logger.warning("缺少入场/出场价格或时间列，跳过交易价格图绘制")

    def _generate_advanced_charts(self, output_path):
        """生成高级分析图表"""

        # 1. 绘制每日收益率分布直方图
        daily_returns = self._get_daily_returns()
        if not daily_returns.empty:
            try:
                fig, ax = plt.subplots(figsize=(12, 6))

                # 计算正负收益的数量
                positive_returns = (daily_returns > 0).sum().iloc[0]
                negative_returns = (daily_returns < 0).sum().iloc[0]
                win_rate = positive_returns / len(daily_returns) if len(daily_returns) > 0 else 0

                # 绘制直方图
                _, bins, patches = ax.hist(daily_returns, bins=50, alpha=0.7, color='#3498db')

                # 为正负收益设置不同颜色
                for i in range(len(patches)):
                    if bins[i] < 0:
                        patches[i].set_facecolor('#e74c3c')  # 负收益为红色
                    else:
                        patches[i].set_facecolor('#2ecc71')  # 正收益为绿色

                # 添加标题和标签
                ax.set_title(f'{self.symbol} - {self.strategy_name} 每日收益率分布', fontsize=14, fontweight='bold')
                ax.set_xlabel('每日收益率', fontsize=12)
                ax.set_ylabel('频数', fontsize=12)

                # 添加统计信息
                ax.text(0.02, 0.95, f'总交易日: {len(daily_returns)}', transform=ax.transAxes, fontsize=10)
                ax.text(0.02, 0.90, f'上涨日: {positive_returns}', transform=ax.transAxes, fontsize=10, color='#2ecc71')
                ax.text(0.02, 0.85, f'下跌日: {negative_returns}', transform=ax.transAxes, fontsize=10, color='#e74c3c')
                ax.text(0.02, 0.80, f'日胜率: {win_rate:.2%}', transform=ax.transAxes, fontsize=10, fontweight='bold')

                # 添加网格线
                ax.grid(True, linestyle='--', alpha=0.7)

                # 格式化x轴为百分比
                ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x*100:.1f}%'))

                plt.tight_layout()
                plt.savefig(os.path.join(output_path, 'daily_returns_hist.png'), dpi=300, bbox_inches='tight')
                plt.close(fig)
                self.logger.debug("每日收益率分布直方图绘制完成")
            except Exception as e:
                self.logger.error(f"绘制每日收益率分布直方图失败: {e}")

        # 2. 绘制每日净值曲线
        daily_values = self._get_daily_values()
        if not daily_values.empty:
            try:
                fig, ax = plt.subplots(figsize=(12, 6))

                # 绘制净值曲线
                daily_values.plot(ax=ax, color='#1f77b4', linewidth=2)

                # 添加标题和标签
                ax.set_title(f'{self.symbol} - {self.strategy_name} 每日净值', fontsize=14, fontweight='bold')
                ax.set_xlabel('日期', fontsize=12)
                ax.set_ylabel('净值', fontsize=12)

                # 添加网格线
                ax.grid(True, linestyle='--', alpha=0.7)

                # 添加起始和结束标记
                ax.scatter(daily_values.index[0], daily_values.iloc[0], color='green', s=100, zorder=5, label='开始')
                ax.scatter(daily_values.index[-1], daily_values.iloc[-1], color='red', s=100, zorder=5, label='结束')

                # 添加图例
                ax.legend(loc='best')

                # 格式化y轴
                ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x:.2f}'))

                plt.tight_layout()
                plt.savefig(os.path.join(output_path, 'daily_values_curve.png'), dpi=300, bbox_inches='tight')
                plt.close(fig)
                self.logger.debug("每日净值曲线绘制完成")
            except Exception as e:
                self.logger.error(f"绘制每日净值曲线失败: {e}")

        # 3. 绘制最大回撤持续时间分布 (如果数据可用)
        # vectorbt.Portfolio.stats() 返回 max_drawdown_duration，但没有分布数据
        # 如果需要分布，需要从原始回撤数据计算
        # print("DEBUG: Plotting drawdown duration distribution...") # Added log
        # drawdown_durations = self.portfolio.drawdown_duration()
        # if not drawdown_durations.empty:
        #     self._plot_chart(
        #         x=drawdown_durations.values,
        #         chart_type='hist',
        #         title=f'{self.symbol} - {self.strategy_name} 回撤持续时间分布',
        #         xlabel='持续时间',
        #         ylabel='频数',
        #         filename='drawdown_duration_hist.png',
        #         output_path=output_path,
        #         bins=50
        #     )
        # print("DEBUG: Drawdown duration distribution plotted.") # Added log

        # 4. 绘制交易量图 (如果数据可用)
        # vectorbt.Portfolio 没有直接提供交易量数据，需要从原始数据获取
        # print("DEBUG: Plotting trade volume...") # Added log
        # if hasattr(self.portfolio, 'trade_volume') and not self.portfolio.trade_volume.empty:
        #      self._plot_chart(
        #         data=self.portfolio.trade_volume,
        #         chart_type='bar',
        #         title=f'{self.symbol} - {self.strategy_name} 交易量',
        #         xlabel='日期',
        #         ylabel='交易量',
        #         filename='trade_volume.png',
        #         output_path=output_path
        #     )
        # print("DEBUG: Trade volume plotted.") # Added log

        # 5. 绘制持仓时间分布 (与交易持续时间分布类似)
        # print("DEBUG: Plotting holding period distribution...") # Added log
        # if 'duration' in trades.columns:
        #     self._plot_chart(
        #         x='duration_seconds',
        #         data=trades,
        #         chart_type='hist',
        #         title=f'{self.symbol} - {self.strategy_name} 持仓时间分布',
        #         xlabel='持仓时间 (秒)',
        #         ylabel='频数',
        #         filename='holding_period_hist.png',
        #         output_path=output_path,
        #         bins=50
        #     )
        # print("DEBUG: Holding period distribution plotted.") # Added log

    def _generate_html_report(self, report_data, output_path):
        """
        生成HTML格式的回测报告

        参数:
            report_data (dict): 报告数据
            output_path (str): 图表输出路径

        返回:
            str: HTML报告内容
        """
        # 定义HTML模板
        template_str = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{{ symbol }} - {{ strategy }} 回测报告</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 20px;
                }
                h1, h2, h3 {
                    color: #2c3e50;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                    border-bottom: 1px solid #eee;
                }
                .section {
                    margin-bottom: 30px;
                    padding: 20px;
                    background-color: #f9f9f9;
                    border-radius: 5px;
                }
                .metrics-container {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }
                .metric-group {
                    flex: 0 0 30%;
                    margin-bottom: 20px;
                }
                .metric {
                    margin-bottom: 10px;
                    padding: 10px;
                    background-color: #fff;
                    border-radius: 3px;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                }
                .metric-name {
                    font-weight: bold;
                    color: #7f8c8d;
                }
                .metric-value {
                    float: right;
                    font-weight: bold;
                    color: #2980b9;
                }
                .positive {
                    color: #27ae60;
                }
                .negative {
                    color: #e74c3c;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    padding: 12px 15px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }
                th {
                    background-color: #f2f2f2;
                    font-weight: bold;
                }
                tr:hover {
                    background-color: #f5f5f5;
                }
                .chart-container {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }
                .chart {
                    flex: 0 0 48%;
                    margin-bottom: 20px;
                    text-align: center;
                }
                .chart img {
                    max-width: 100%;
                    height: auto;
                    border: 1px solid #ddd;
                    border-radius: 3px;
                }
                .footer {
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #eee;
                    color: #7f8c8d;
                    font-size: 0.9em;
                }
                @media (max-width: 768px) {
                    .metric-group, .chart {
                        flex: 0 0 100%;
                    }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{{ symbol }} - {{ strategy }} 回测报告</h1>
                <p>报告生成时间: {{ report_date }}</p>
            </div>

            <div class="section">
                <h2>绩效概览</h2>
                <div class="metrics-container">
                    <div class="metric-group">
                        <h3>收益指标</h3>
                        <div class="metric">
                            <span class="metric-name">总收益率</span>
                            <span class="metric-value {% if metrics.total_return > 0 %}positive{% else %}negative{% endif %}">{{ (metrics.total_return * 100)|round(2) }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">年化收益率</span>
                            <span class="metric-value {% if metrics.annual_return > 0 %}positive{% else %}negative{% endif %}">{{ (metrics.annual_return * 100)|round(2) }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">日均收益率</span>
                            <span class="metric-value {% if metrics.daily_return > 0 %}positive{% else %}negative{% endif %}">{{ (metrics.daily_return * 100)|round(4) }}%</span>
                        </div>
                    </div>

                    <div class="metric-group">
                        <h3>风险指标</h3>
                        <div class="metric">
                            <span class="metric-name">最大回撤</span>
                            <span class="metric-value negative">{{ (metrics.max_drawdown * 100)|round(2) }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">年化波动率</span>
                            <span class="metric-value">{{ (metrics.volatility_annual * 100)|round(2) }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">夏普比率</span>
                            <span class="metric-value {% if metrics.sharpe_ratio > 0 %}positive{% else %}negative{% endif %}">{{ metrics.sharpe_ratio|round(2) }}</span>
                        </div>
                    </div>

                    <div class="metric-group">
                        <h3>交易指标</h3>
                        <div class="metric">
                            <span class="metric-name">交易次数</span>
                            <span class="metric-value">{{ metrics.num_trades }}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">胜率</span>
                            <span class="metric-value {% if metrics.win_rate > 0.5 %}positive{% else %}negative{% endif %}">{{ (metrics.win_rate * 100)|round(2) }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">盈亏比</span>
                            <span class="metric-value {% if metrics.profit_factor > 1 %}positive{% else %}negative{% endif %}">{{ metrics.profit_factor|round(2) }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>图表分析</h2>
                <div class="chart-container">
                    <div class="chart">
                        <h3>净值曲线</h3>
                        <img src="equity_curve.png" alt="净值曲线">
                    </div>
                    <div class="chart">
                        <h3>回撤分析</h3>
                        <img src="drawdowns.png" alt="回撤分析">
                    </div>
                    <div class="chart">
                        <h3>月度收益热力图</h3>
                        <img src="monthly_returns_heatmap.png" alt="月度收益热力图">
                    </div>
                    <div class="chart">
                        <h3>日收益分布</h3>
                        <img src="daily_returns_hist.png" alt="日收益分布">
                    </div>
                </div>
            </div>

            {% if trades|length > 0 %}
            <div class="section">
                <h2>交易分析</h2>
                <div class="chart-container">
                    <div class="chart">
                        <h3>交易收益分布</h3>
                        <img src="trade_returns_hist.png" alt="交易收益分布">
                    </div>
                    <div class="chart">
                        <h3>交易持续时间分布</h3>
                        <img src="trade_duration_hist.png" alt="交易持续时间分布">
                    </div>
                </div>

                <h3>最近交易记录</h3>
                <table>
                    <thead>
                        <tr>
                            <th>开仓时间</th>
                            <th>平仓时间</th>
                            <th>持仓时间</th>
                            <th>收益率</th>
                            <th>盈亏</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for trade in trades[:10] %}
                        <tr>
                            <td>{{ trade.entry_time }}</td>
                            <td>{{ trade.exit_time }}</td>
                            <td>{{ trade.duration if trade.duration is defined else '-' }}</td>
                            <td class="{% if trade.return is defined and trade.return > 0 %}positive{% elif trade.return is defined and trade.return < 0 %}negative{% endif %}">
                                {% if trade.return is defined %}
                                    {{ (trade.return * 100)|round(2) }}%
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td class="{% if trade.pnl is defined and trade.pnl > 0 %}positive{% elif trade.pnl is defined and trade.pnl < 0 %}negative{% endif %}">
                                {% if trade.pnl is defined %}
                                    {{ trade.pnl|round(2) }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% endif %}

            <div class="section">
                <h2>详细指标</h2>
                <div class="metrics-container">
                    <div class="metric-group">
                        <h3>收益指标</h3>
                        {% for key, value in metrics.items() %}
                            {% if key in ['total_return', 'annual_return', 'daily_return', 'max_daily_profit', 'max_daily_loss'] %}
                            <div class="metric">
                                <span class="metric-name">{{ key }}</span>
                                <span class="metric-value {% if value > 0 and key != 'max_daily_loss' %}positive{% elif value < 0 or key == 'max_daily_loss' %}negative{% endif %}">
                                    {% if 'return' in key or 'drawdown' in key or 'daily' in key %}
                                        {{ (value * 100)|round(2) }}%
                                    {% else %}
                                        {{ value|round(4) }}
                                    {% endif %}
                                </span>
                            </div>
                            {% endif %}
                        {% endfor %}
                    </div>

                    <div class="metric-group">
                        <h3>风险指标</h3>
                        {% for key, value in metrics.items() %}
                            {% if key in ['max_drawdown', 'max_drawdown_duration', 'volatility_annual', 'sharpe_ratio', 'sortino_ratio', 'calmar_ratio', 'omega_ratio'] %}
                            <div class="metric">
                                <span class="metric-name">{{ key }}</span>
                                <span class="metric-value {% if key == 'max_drawdown' or key == 'max_drawdown_duration' %}negative{% elif value > 0 %}positive{% else %}negative{% endif %}">
                                    {% if key == 'max_drawdown' or key == 'volatility_annual' %}
                                        {{ (value * 100)|round(2) }}%
                                    {% else %}
                                        {{ value|round(2) }}
                                    {% endif %}
                                </span>
                            </div>
                            {% endif %}
                        {% endfor %}
                    </div>

                    <div class="metric-group">
                        <h3>交易指标</h3>
                        {% for key, value in metrics.items() %}
                            {% if key in ['win_rate', 'profit_factor', 'expectancy', 'avg_win', 'avg_loss', 'best_trade', 'worst_trade', 'avg_trade', 'num_trades', 'max_win_streak', 'max_loss_streak'] %}
                            <div class="metric">
                                <span class="metric-name">{{ key }}</span>
                                <span class="metric-value {% if key in ['win_rate', 'profit_factor', 'expectancy', 'avg_win', 'best_trade', 'max_win_streak'] and value > 0 %}positive{% elif key in ['avg_loss', 'worst_trade', 'max_loss_streak'] and value < 0 %}negative{% endif %}">
                                    {% if key == 'win_rate' %}
                                        {{ (value * 100)|round(2) }}%
                                    {% elif key in ['num_trades', 'max_win_streak', 'max_loss_streak'] %}
                                        {{ value|int }}
                                    {% else %}
                                        {{ value|round(4) }}
                                    {% endif %}
                                </span>
                            </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>

            <div class="footer">
                <p>由 QuantEngine 生成 | {{ report_date }}</p>
            </div>
        </body>
        </html>
        """

        # 使用Jinja2渲染模板
        template = jinja2.Template(template_str)
        html_content = template.render(
            symbol=report_data['symbol'],
            strategy=report_data['strategy'],
            metrics=report_data['metrics'],
            trades=report_data['trades'],
            report_date=report_data['report_date']
        )

        return html_content

    def _convert_html_to_pdf(self, html_path, pdf_path):
        """
        将HTML报告转换为PDF

        参数:
            html_path (str): HTML文件路径
            pdf_path (str): 输出PDF文件路径
        """
        self.logger.info(f"尝试将HTML报告转换为PDF: {html_path} -> {pdf_path}")

        # 定义一个函数来尝试使用不同的PDF转换库
        def try_convert_with_weasyprint():
            try:
                # 动态导入weasyprint以避免IDE警告
                weasyprint = __import__('weasyprint')
                weasyprint.HTML(html_path).write_pdf(pdf_path)
                self.logger.info("使用weasyprint成功生成PDF报告")
                return True
            except ImportError:
                self.logger.warning("weasyprint库未安装，尝试其他方法")
                return False
            except Exception as e:
                self.logger.error(f"使用weasyprint生成PDF报告失败: {str(e)}")
                return False

        def try_convert_with_pdfkit():
            try:
                # 动态导入pdfkit以避免IDE警告
                pdfkit = __import__('pdfkit')
                pdfkit.from_file(html_path, pdf_path)
                self.logger.info("使用pdfkit成功生成PDF报告")
                return True
            except ImportError:
                self.logger.warning("pdfkit库未安装，尝试其他方法")
                return False
            except Exception as e:
                self.logger.error(f"使用pdfkit生成PDF报告失败: {str(e)}")
                return False

        # 尝试使用不同的库生成PDF
        if not try_convert_with_weasyprint() and not try_convert_with_pdfkit():
            error_msg = "无法生成PDF报告，请安装 weasyprint 或 pdfkit 库"
            self.logger.error(error_msg)
            raise ImportError(error_msg)



















