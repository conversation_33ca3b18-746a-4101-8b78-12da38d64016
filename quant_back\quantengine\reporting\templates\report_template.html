<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ strategy_name }} - {{ symbol }} ({{ start_date }} 至 {{ end_date }})</title>
    <script src="https://cdn.plot.ly/plotly-2.35.2.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .dashboard { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .chart { border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
        .chart-title { font-size: 16px; margin-bottom: 10px; font-weight: bold; }
        .metrics-table { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>{{ strategy_name }} - {{ symbol }} ({{ start_date }} 至 {{ end_date }})</h1>
    
    <div class="dashboard">
        {% for chart in charts %}
        <div class="chart" id="chart-{{ loop.index }}">
            <div class="chart-title">{{ chart.title }}</div>
            <div id="plot-{{ loop.index }}"></div>
        </div>
        {% endfor %}
    </div>

    <script>
        // 从JSON数据渲染图表
        const reportData = JSON.parse('{{ report_data|tojson|safe }}');
        
        reportData.charts.forEach((chart, index) => {
            Plotly.newPlot(
                `plot-${index + 1}`,
                chart.data,
                chart.layout,
                { displayModeBar: true, scrollZoom: true }
            );
        });
    </script>
</body>
</html>