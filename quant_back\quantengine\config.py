import json
from pathlib import Path
from typing import Dict, Any

class ConfigManager:
    """配置管理系统"""

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ConfigManager, cls).__new__(cls)
            cls._instance._init_config()
        return cls._instance

    def _init_config(self):
        """初始化配置"""
        self._config = {
            # 数据源配置
            "data_sources": {
                "akshare": {
                    "retry_times": 3,
                    "timeout": 30
                },
                "db": {
                    "connection_string": "sqlite:///quantengine.db"
                }
            },

            # 回测配置
            "backtest": {
                "default_initial_capital": 100000.0,
                "default_commission": 0.0003,
                "default_slippage": 0.001,
                "default_position_size": 1.0
            },

            # 策略配置
            "strategies": {
                "default_params": {
                    "ma_cross": {
                        "fast_ma": 5,
                        "slow_ma": 20
                    },
                    "dual_ma": {
                        "short_ma": 10,
                        "long_ma": 30
                    },
                    "rsi_strategy": {
                        "rsi_period": 14,
                        "overbought": 70,
                        "oversold": 30
                    }
                }
            },

            # 日志配置
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(levelname)s - %(message)s"
            }
        }

        # 尝试加载用户自定义配置
        self._load_user_config()

    def _load_user_config(self):
        """加载用户自定义配置"""
        config_path = Path(__file__).parent.parent / "config.json"
        if config_path.exists():
            try:
                with open(config_path, "r", encoding="utf-8") as f:
                    user_config = json.load(f)
                    self._merge_configs(user_config)
            except Exception as e:
                print(f"加载用户配置失败: {str(e)}")

    def _merge_configs(self, user_config: Dict[str, Any]):
        """合并用户配置"""
        for section, values in user_config.items():
            if section in self._config:
                if isinstance(values, dict):
                    self._config[section].update(values)
                else:
                    self._config[section] = values
            else:
                self._config[section] = values

    def get(self, key: str, default=None) -> Any:
        """获取配置项"""
        keys = key.split(".")
        value = self._config

        try:
            for k in keys:
                value = value[k]
            return value
        except KeyError:
            return default

    def set(self, key: str, value: Any):
        """设置配置项"""
        keys = key.split(".")
        current = self._config

        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]

        current[keys[-1]] = value

    def save(self):
        """保存用户配置"""
        config_path = Path(__file__).parent.parent / "config.json"
        with open(config_path, "w", encoding="utf-8") as f:
            json.dump(self._config, f, indent=4)
