2025-05-22 08:21:23 - quantengine - INFO - 数据源加载完成
2025-05-22 08:21:23 - quantengine - INFO - 正在加载策略: dual_ma_strategy
2025-05-22 08:21:23 - quantengine - ERROR - 策略加载失败: 不支持的策略名称: dual_ma_strategy，请检查策略名称是否正确
2025-05-22 08:22:38 - quantengine - INFO - 数据源加载完成
2025-05-22 08:22:38 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 08:22:38 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 08:22:38 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 08:22:38 - quantengine - INFO - 开始执行回测...
2025-05-22 08:22:38 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2023-12-31
2025-05-22 08:22:38 - quantengine - INFO - 数据获取完成，共970条记录
2025-05-22 08:22:38 - quantengine - INFO - 开始执行策略计算...
2025-05-22 08:22:39 - quantengine.strategy - INFO - Generated 468 entry signals and 483 exit signals
2025-05-22 08:22:39 - quantengine - INFO - 策略计算完成，生成468个入场信号和483个出场信号
2025-05-22 08:22:39 - quantengine - INFO - 开始执行回测计算...
2025-05-22 08:22:44 - quantengine - INFO - 生成了 24 条交易记录
2025-05-22 08:22:44 - quantengine - INFO - 回测完成，最终资产:106974.03
2025-05-22 08:22:44 - quantengine - INFO - 回测执行完成
2025-05-22 08:22:44 - quantengine - INFO - 开始生成报告...
2025-05-22 08:22:44 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 08:22:47 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 08:22:47 - quantengine.report - INFO - 报告生成完成
2025-05-22 08:22:47 - quantengine - INFO - 报告生成完成，已保存至 output
2025-05-22 08:52:51 - quantengine - INFO - 数据源加载完成
2025-05-22 08:52:51 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 08:52:51 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 08:52:51 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 08:52:51 - quantengine - INFO - 开始执行回测...
2025-05-22 08:52:51 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-22 08:52:51 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-22 08:52:51 - quantengine - INFO - 开始执行策略计算...
2025-05-22 08:52:53 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-22 08:52:53 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-22 08:52:53 - quantengine - INFO - 开始执行回测计算...
2025-05-22 08:52:58 - quantengine - INFO - 生成了 5 条交易记录
2025-05-22 08:52:58 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-22 08:52:58 - quantengine - INFO - 回测执行完成
2025-05-22 08:52:58 - quantengine - INFO - 开始生成报告...
2025-05-22 08:52:58 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 08:53:01 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 08:53:02 - quantengine.report - INFO - 报告生成完成
2025-05-22 08:53:02 - quantengine - INFO - 报告生成完成，已保存至 output
2025-05-22 09:43:32 - quantengine - INFO - 数据源加载完成
2025-05-22 09:43:32 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 09:43:32 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 09:43:32 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 09:43:32 - quantengine - INFO - 开始执行回测...
2025-05-22 09:43:32 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-22 09:43:33 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-22 09:43:33 - quantengine - INFO - 开始执行策略计算...
2025-05-22 09:43:34 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-22 09:43:34 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-22 09:43:34 - quantengine - INFO - 开始执行回测计算...
2025-05-22 09:43:39 - quantengine - INFO - 生成了 5 条交易记录
2025-05-22 09:43:39 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-22 09:43:39 - quantengine - INFO - 回测执行完成
2025-05-22 09:43:39 - quantengine - INFO - 开始生成报告...
2025-05-22 09:43:39 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 09:43:42 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 09:45:51 - quantengine - INFO - 数据源加载完成
2025-05-22 09:45:51 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 09:45:51 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 09:45:51 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 09:45:51 - quantengine - INFO - 开始执行回测...
2025-05-22 09:45:51 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-22 09:45:51 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-22 09:45:51 - quantengine - INFO - 开始执行策略计算...
2025-05-22 09:45:53 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-22 09:45:53 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-22 09:45:53 - quantengine - INFO - 开始执行回测计算...
2025-05-22 09:45:59 - quantengine - INFO - 生成了 5 条交易记录
2025-05-22 09:45:59 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-22 09:45:59 - quantengine - INFO - 回测执行完成
2025-05-22 09:45:59 - quantengine - INFO - 开始生成报告...
2025-05-22 09:45:59 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 09:46:02 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 09:46:54 - quantengine - INFO - 数据源加载完成
2025-05-22 09:46:54 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 09:46:54 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 09:46:54 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 09:46:54 - quantengine - INFO - 开始执行回测...
2025-05-22 09:46:54 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-22 09:46:54 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-22 09:46:54 - quantengine - INFO - 开始执行策略计算...
2025-05-22 09:46:56 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-22 09:46:56 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-22 09:46:56 - quantengine - INFO - 开始执行回测计算...
2025-05-22 09:47:07 - quantengine - INFO - 生成了 5 条交易记录
2025-05-22 09:47:07 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-22 09:47:07 - quantengine - INFO - 回测执行完成
2025-05-22 09:47:07 - quantengine - INFO - 开始生成报告...
2025-05-22 09:47:07 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 09:47:15 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 09:48:17 - quantengine - INFO - 数据源加载完成
2025-05-22 09:48:17 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 09:48:17 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 09:48:17 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 09:48:17 - quantengine - INFO - 开始执行回测...
2025-05-22 09:48:17 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-22 09:48:17 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-22 09:48:17 - quantengine - INFO - 开始执行策略计算...
2025-05-22 09:48:19 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-22 09:48:19 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-22 09:48:19 - quantengine - INFO - 开始执行回测计算...
2025-05-22 09:48:30 - quantengine - INFO - 生成了 5 条交易记录
2025-05-22 09:48:30 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-22 09:48:30 - quantengine - INFO - 回测执行完成
2025-05-22 09:48:30 - quantengine - INFO - 开始生成报告...
2025-05-22 09:48:30 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 09:48:38 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 09:53:48 - quantengine - INFO - 数据源加载完成
2025-05-22 09:53:48 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 09:53:48 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 09:53:48 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 09:53:48 - quantengine - INFO - 开始执行回测...
2025-05-22 09:53:48 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-22 09:53:49 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-22 09:53:49 - quantengine - INFO - 开始执行策略计算...
2025-05-22 09:53:51 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-22 09:53:51 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-22 09:53:51 - quantengine - INFO - 开始执行回测计算...
2025-05-22 09:54:05 - quantengine - INFO - 生成了 5 条交易记录
2025-05-22 09:54:05 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-22 09:54:05 - quantengine - INFO - 回测执行完成
2025-05-22 09:54:05 - quantengine - INFO - 开始生成报告...
2025-05-22 09:54:05 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 09:54:09 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 20:01:46 - quantengine - INFO - 数据源加载完成
2025-05-22 20:01:46 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 20:01:46 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 20:01:46 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 20:01:46 - quantengine - INFO - 开始执行回测...
2025-05-22 20:01:46 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-22 20:01:46 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-22 20:01:46 - quantengine - INFO - 开始执行策略计算...
2025-05-22 20:01:48 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-22 20:01:48 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-22 20:01:48 - quantengine - INFO - 开始执行回测计算...
2025-05-22 20:02:01 - quantengine - INFO - 生成了 5 条交易记录
2025-05-22 20:02:01 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-22 20:02:01 - quantengine - INFO - 回测执行完成
2025-05-22 20:02:01 - quantengine - INFO - 开始生成报告...
2025-05-22 20:02:01 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 20:02:13 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 20:07:10 - quantengine - INFO - 数据源加载完成
2025-05-22 20:07:10 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 20:07:10 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 20:07:10 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 20:07:10 - quantengine - INFO - 开始执行回测...
2025-05-22 20:07:10 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-22 20:07:11 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-22 20:07:11 - quantengine - INFO - 开始执行策略计算...
2025-05-22 20:07:13 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-22 20:07:13 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-22 20:07:13 - quantengine - INFO - 开始执行回测计算...
2025-05-22 20:07:26 - quantengine - INFO - 生成了 5 条交易记录
2025-05-22 20:07:26 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-22 20:07:26 - quantengine - INFO - 回测执行完成
2025-05-22 20:07:26 - quantengine - INFO - 开始生成报告...
2025-05-22 20:07:26 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 20:07:32 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 20:12:13 - quantengine - INFO - 数据源加载完成
2025-05-22 20:12:13 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 20:12:13 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 20:12:13 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 20:12:13 - quantengine - INFO - 开始执行回测...
2025-05-22 20:12:13 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-22 20:12:13 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-22 20:12:13 - quantengine - INFO - 开始执行策略计算...
2025-05-22 20:12:15 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-22 20:12:15 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-22 20:12:15 - quantengine - INFO - 开始执行回测计算...
2025-05-22 20:12:23 - quantengine - INFO - 生成了 5 条交易记录
2025-05-22 20:12:23 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-22 20:12:23 - quantengine - INFO - 回测执行完成
2025-05-22 20:12:23 - quantengine - INFO - 开始生成报告...
2025-05-22 20:12:23 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 20:12:28 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 22:05:53 - quantengine - INFO - 数据源加载完成
2025-05-22 22:05:53 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 22:05:53 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 22:05:53 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 22:05:53 - quantengine - INFO - 开始执行回测...
2025-05-22 22:05:53 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-22 22:05:53 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-22 22:05:53 - quantengine - INFO - 开始执行策略计算...
2025-05-22 22:05:55 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-22 22:05:55 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-22 22:05:55 - quantengine - INFO - 开始执行回测计算...
2025-05-22 22:05:59 - quantengine - INFO - 生成了 5 条交易记录
2025-05-22 22:05:59 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-22 22:05:59 - quantengine - INFO - 回测执行完成
2025-05-22 22:05:59 - quantengine - INFO - 开始生成报告...
2025-05-22 22:05:59 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 22:06:02 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 22:09:27 - quantengine - INFO - 数据源加载完成
2025-05-22 22:09:27 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 22:09:27 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 22:09:27 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 22:09:27 - quantengine - INFO - 开始执行回测...
2025-05-22 22:09:27 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-22 22:09:28 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-22 22:09:28 - quantengine - INFO - 开始执行策略计算...
2025-05-22 22:09:28 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-22 22:09:28 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-22 22:09:28 - quantengine - INFO - 开始执行回测计算...
2025-05-22 22:09:32 - quantengine - INFO - 生成了 5 条交易记录
2025-05-22 22:09:32 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-22 22:09:32 - quantengine - INFO - 回测执行完成
2025-05-22 22:09:32 - quantengine - INFO - 开始生成报告...
2025-05-22 22:09:32 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 22:09:35 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 22:13:37 - quantengine - INFO - 数据源加载完成
2025-05-22 22:13:37 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 22:13:37 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 22:13:37 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 22:13:37 - quantengine - INFO - 开始执行回测...
2025-05-22 22:13:37 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-22 22:13:37 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-22 22:13:37 - quantengine - INFO - 开始执行策略计算...
2025-05-22 22:13:38 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-22 22:13:38 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-22 22:13:38 - quantengine - INFO - 开始执行回测计算...
2025-05-22 22:13:42 - quantengine - INFO - 生成了 5 条交易记录
2025-05-22 22:13:42 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-22 22:13:42 - quantengine - INFO - 回测执行完成
2025-05-22 22:13:42 - quantengine - INFO - 开始生成报告...
2025-05-22 22:13:42 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 22:13:45 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 22:16:59 - quantengine - INFO - 数据源加载完成
2025-05-22 22:16:59 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 22:16:59 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 22:16:59 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 22:16:59 - quantengine - INFO - 开始执行回测...
2025-05-22 22:16:59 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-22 22:16:59 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-22 22:16:59 - quantengine - INFO - 开始执行策略计算...
2025-05-22 22:17:00 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-22 22:17:00 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-22 22:17:00 - quantengine - INFO - 开始执行回测计算...
2025-05-22 22:17:03 - quantengine - INFO - 生成了 5 条交易记录
2025-05-22 22:17:03 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-22 22:17:03 - quantengine - INFO - 回测执行完成
2025-05-22 22:17:03 - quantengine - INFO - 开始生成报告...
2025-05-22 22:17:03 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 22:17:06 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 22:18:13 - quantengine - INFO - 数据源加载完成
2025-05-22 22:18:13 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 22:18:13 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 22:18:13 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 22:18:13 - quantengine - INFO - 开始执行回测...
2025-05-22 22:18:13 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-22 22:18:13 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-22 22:18:13 - quantengine - INFO - 开始执行策略计算...
2025-05-22 22:18:14 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-22 22:18:14 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-22 22:18:14 - quantengine - INFO - 开始执行回测计算...
2025-05-22 22:18:18 - quantengine - INFO - 生成了 5 条交易记录
2025-05-22 22:18:18 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-22 22:18:18 - quantengine - INFO - 回测执行完成
2025-05-22 22:18:18 - quantengine - INFO - 开始生成报告...
2025-05-22 22:18:18 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 22:18:21 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 22:19:32 - quantengine - INFO - 数据源加载完成
2025-05-22 22:19:32 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 22:19:32 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 22:19:32 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 22:19:32 - quantengine - INFO - 开始执行回测...
2025-05-22 22:19:32 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-22 22:19:32 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-22 22:19:32 - quantengine - INFO - 开始执行策略计算...
2025-05-22 22:19:33 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-22 22:19:33 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-22 22:19:33 - quantengine - INFO - 开始执行回测计算...
2025-05-22 22:19:38 - quantengine - INFO - 生成了 5 条交易记录
2025-05-22 22:19:38 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-22 22:19:38 - quantengine - INFO - 回测执行完成
2025-05-22 22:19:38 - quantengine - INFO - 开始生成报告...
2025-05-22 22:19:38 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 22:19:41 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 22:21:43 - quantengine - INFO - 数据源加载完成
2025-05-22 22:21:43 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 22:21:43 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 22:21:43 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 22:21:43 - quantengine - INFO - 开始执行回测...
2025-05-22 22:21:43 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-22 22:21:44 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-22 22:21:44 - quantengine - INFO - 开始执行策略计算...
2025-05-22 22:21:45 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-22 22:21:45 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-22 22:21:45 - quantengine - INFO - 开始执行回测计算...
2025-05-22 22:21:53 - quantengine - INFO - 生成了 5 条交易记录
2025-05-22 22:21:53 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-22 22:21:53 - quantengine - INFO - 回测执行完成
2025-05-22 22:21:53 - quantengine - INFO - 开始生成报告...
2025-05-22 22:21:53 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 22:21:57 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 22:28:40 - quantengine - INFO - 数据源加载完成
2025-05-22 22:28:40 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 22:28:40 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 22:28:40 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 22:28:40 - quantengine - INFO - 开始执行回测...
2025-05-22 22:28:40 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-22 22:28:40 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-22 22:28:40 - quantengine - INFO - 开始执行策略计算...
2025-05-22 22:28:42 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-22 22:28:42 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-22 22:28:42 - quantengine - INFO - 开始执行回测计算...
2025-05-22 22:28:54 - quantengine - INFO - 生成了 5 条交易记录
2025-05-22 22:28:54 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-22 22:28:54 - quantengine - INFO - 回测执行完成
2025-05-22 22:28:54 - quantengine - INFO - 开始生成报告...
2025-05-22 22:28:54 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 22:28:59 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 22:30:30 - quantengine - INFO - 数据源加载完成
2025-05-22 22:30:30 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 22:30:30 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 22:30:30 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 22:30:30 - quantengine - INFO - 开始执行回测...
2025-05-22 22:30:30 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-22 22:30:30 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-22 22:30:30 - quantengine - INFO - 开始执行策略计算...
2025-05-22 22:30:32 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-22 22:30:32 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-22 22:30:32 - quantengine - INFO - 开始执行回测计算...
2025-05-22 22:30:42 - quantengine - INFO - 生成了 5 条交易记录
2025-05-22 22:30:42 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-22 22:30:42 - quantengine - INFO - 回测执行完成
2025-05-22 22:30:42 - quantengine - INFO - 开始生成报告...
2025-05-22 22:30:42 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 22:30:49 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 22:31:24 - quantengine - INFO - 数据源加载完成
2025-05-22 22:31:24 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-22 22:31:24 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-22 22:31:24 - quantengine - INFO - 正在创建输出目录: output
2025-05-22 22:31:24 - quantengine - INFO - 开始执行回测...
2025-05-22 22:31:24 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-22 22:31:24 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-22 22:31:24 - quantengine - INFO - 开始执行策略计算...
2025-05-22 22:31:26 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-22 22:31:26 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-22 22:31:26 - quantengine - INFO - 开始执行回测计算...
2025-05-22 22:31:31 - quantengine - INFO - 生成了 5 条交易记录
2025-05-22 22:31:31 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-22 22:31:31 - quantengine - INFO - 回测执行完成
2025-05-22 22:31:31 - quantengine - INFO - 开始生成报告...
2025-05-22 22:31:31 - quantengine.report - INFO - 开始生成回测报告...
2025-05-22 22:31:35 - quantengine.report - INFO - 将报告保存到 output
2025-05-22 22:31:35 - quantengine.report - INFO - 报告生成完成
2025-05-22 22:31:35 - quantengine - INFO - 报告生成完成，已保存至 output
