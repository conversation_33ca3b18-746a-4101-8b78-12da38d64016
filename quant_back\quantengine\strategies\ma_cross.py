import pandas as pd
import numpy as np
import vectorbt as vbt
from .base_strategy import BaseStrategy

class MACrossStrategy(BaseStrategy):
    """均线交叉策略"""
    
    def __init__(self, params=None):
        super().__init__(params)
        self.fast_window = self.params.get('fast_window', 5)
        self.slow_window = self.params.get('slow_window', 20)
    
    @staticmethod
    def get_params_schema():
        return {
            "fast_window": {
                "type": "integer",
                "title": "快线周期",
                "default": 5,
                "minimum": 2,
                "maximum": 100
            },
            "slow_window": {
                "type": "integer",
                "title": "慢线周期",
                "default": 20,
                "minimum": 5,
                "maximum": 200
            }
        }
    
    def run(self, data):
        # 计算快速和慢速移动平均线
        fast_ma = vbt.MA.run(data['close'], window=self.fast_window)
        slow_ma = vbt.MA.run(data['close'], window=self.slow_window)
        
        # 生成交叉信号
        entries = fast_ma.ma_above(slow_ma)
        exits = fast_ma.ma_below(slow_ma)
        
        return entries, exits