import argparse
import logging
import importlib
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Type

from .data.data_source import DataSource
from .data.akshare_source import AkshareSource
from .data.db_source import DBSource
from .data.mock_source import MockSource
from .strategies.base_strategy import BaseStrategy
from .backtest.enhanced_engine import EnhancedBacktestEngine
from .backtest.enhanced_report import EnhancedReportGenerator
from .utils.logger import setup_logger
from .config import ConfigManager
from .risk import RiskManager, RiskLimitType

def load_strategy(strategy_name: str) -> Type[BaseStrategy]:
    """
    动态加载策略类

    参数:
        strategy_name (str): 策略名称

    返回:
        Type[BaseStrategy]: 策略类
    """
    try:
        # 尝试从strategies包中导入
        module = importlib.import_module(f'.strategies.{strategy_name.lower()}', package='quantengine')

        # 查找策略类
        for attr_name in dir(module):
            attr = getattr(module, attr_name)
            if isinstance(attr, type) and issubclass(attr, BaseStrategy) and attr != BaseStrategy:
                return attr

        raise ValueError(f"在模块 {strategy_name.lower()} 中未找到策略类")
    except (ImportError, AttributeError) as e:
        logging.error(f"加载策略 {strategy_name} 失败: {str(e)}")
        raise ValueError(f"策略 {strategy_name} 不存在或无法加载")

def get_data_source(source_type: str, **kwargs) -> DataSource:
    """
    获取数据源实例

    参数:
        source_type (str): 数据源类型
        **kwargs: 数据源参数

    返回:
        DataSource: 数据源实例
    """
    if source_type.lower() == 'akshare':
        return AkshareSource(**kwargs)
    elif source_type.lower() == 'db':
        return DBSource(**kwargs)
    elif source_type.lower() == 'mock':
        return MockDataSource(**kwargs)
    else:
        raise ValueError(f"不支持的数据源类型: {source_type}")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='量化回测引擎')

    # 基本参数
    parser.add_argument('--strategy', type=str, required=True, help='策略名称')
    parser.add_argument('--symbol', type=str, required=True, help='证券代码')
    parser.add_argument('--start', type=str, required=True, help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end', type=str, required=True, help='结束日期 (YYYY-MM-DD)')

    # 数据源参数
    parser.add_argument('--data-source', type=str, default='akshare', help='数据源类型 (akshare, db, mock)')
    parser.add_argument('--timeframe', type=str, default='1d', help='时间周期 (1d, 1h, etc.)')

    # 回测参数
    parser.add_argument('--initial-capital', type=float, help='初始资金')
    parser.add_argument('--commission', type=float, help='手续费率')
    parser.add_argument('--slippage', type=float, help='滑点率')
    parser.add_argument('--position-size', type=float, help='仓位比例 (0-1)')

    # 策略参数
    parser.add_argument('--param', action='append', help='策略参数，格式为name=value')

    # 风险管理参数
    parser.add_argument('--max-drawdown', type=float, help='最大回撤限制 (0-1)')
    parser.add_argument('--stop-loss', type=float, help='止损比例 (0-1)')
    parser.add_argument('--take-profit', type=float, help='止盈比例 (0-1)')
    parser.add_argument('--no-risk-management', action='store_true', help='禁用风险管理')

    # 优化参数
    parser.add_argument('--optimize', action='store_true', help='优化策略参数')
    parser.add_argument('--optimize-param', action='append', help='要优化的参数，格式为name=min,max,step')
    parser.add_argument('--optimize-metric', type=str, default='sharpe_ratio', help='优化指标')

    # 多资产参数
    parser.add_argument('--symbols', type=str, help='多个证券代码，用逗号分隔')
    parser.add_argument('--parallel', action='store_true', help='并行处理多资产')

    # 缓存参数
    parser.add_argument('--no-cache', action='store_true', help='禁用数据缓存')
    parser.add_argument('--cache-expiry', type=int, help='缓存过期时间（天）')

    # 输出参数
    parser.add_argument('--output-format', type=str, default='html', choices=['html', 'json', 'both'], help='输出格式')
    parser.add_argument('--output-file', type=str, help='输出文件名')

    # 日志参数
    parser.add_argument('--log-level', type=str, default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], help='日志级别')

    return parser.parse_args()

def parse_strategy_params(param_args: Optional[List[str]]) -> Dict[str, Any]:
    """
    解析策略参数

    参数:
        param_args (List[str]): 参数列表，格式为['name=value', ...]

    返回:
        Dict[str, Any]: 参数字典
    """
    params = {}
    if param_args:
        for param in param_args:
            try:
                name, value = param.split('=')
                # 尝试转换为数值类型
                try:
                    if '.' in value:
                        params[name] = float(value)
                    else:
                        params[name] = int(value)
                except ValueError:
                    # 如果不是数值，保持字符串类型
                    params[name] = value
            except ValueError:
                logging.warning(f"无效的参数格式: {param}，应为name=value")

    return params

def parse_optimize_params(optimize_args: Optional[List[str]]) -> Dict[str, List[float]]:
    """
    解析优化参数

    参数:
        optimize_args (List[str]): 参数列表，格式为['name=min,max,step', ...]

    返回:
        Dict[str, List[float]]: 参数范围字典
    """
    params = {}
    if optimize_args:
        for param in optimize_args:
            try:
                name, range_str = param.split('=')
                min_val, max_val, step = map(float, range_str.split(','))

                # 生成参数值列表
                values = []
                current = min_val
                while current <= max_val:
                    values.append(current)
                    current += step

                params[name] = values
            except ValueError:
                logging.warning(f"无效的优化参数格式: {param}，应为name=min,max,step")

    return params

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 设置日志
    setup_logger(name='quantengine', log_level=args.log_level)
    logger = logging.getLogger('quantengine')

    # 加载配置
    config = ConfigManager()

    try:
        # 加载策略类
        strategy_class = load_strategy(args.strategy)
        logger.info(f"加载策略: {args.strategy}")

        # 解析策略参数
        strategy_params = parse_strategy_params(args.param)
        logger.info(f"策略参数: {strategy_params}")

        # 创建策略实例
        strategy = strategy_class(**strategy_params)

        # 获取数据源
        data_source = get_data_source(args.data_source)
        logger.info(f"使用数据源: {args.data_source}")

        # 创建回测引擎
        engine = EnhancedBacktestEngine(
            data_source=data_source,
            strategy=strategy,
            initial_capital=args.initial_capital,
            commission=args.commission,
            slippage=args.slippage,
            position_size=args.position_size,
            use_cache=not args.no_cache,
            cache_expiry=args.cache_expiry
        )

        # 设置风险管理参数
        if not args.no_risk_management:
            if args.max_drawdown is not None:
                engine.risk_manager.set_risk_limit(RiskLimitType.MAX_DRAWDOWN, args.max_drawdown)

            if args.stop_loss is not None:
                engine.risk_manager.set_risk_limit(RiskLimitType.STOP_LOSS, args.stop_loss)

            if args.take_profit is not None:
                engine.risk_manager.set_risk_limit(RiskLimitType.TAKE_PROFIT, args.take_profit)

        # 执行回测
        if args.optimize:
            # 解析优化参数
            optimize_params = parse_optimize_params(args.optimize_param)
            if not optimize_params:
                logger.error("未指定优化参数")
                sys.exit(1)

            logger.info(f"开始优化策略参数: {optimize_params}")
            result = engine.optimize_strategy_params(
                symbol=args.symbol,
                start_date=args.start,
                end_date=args.end,
                param_grid=optimize_params,
                timeframe=args.timeframe,
                metric=args.optimize_metric
            )

            # 输出优化结果
            logger.info(f"优化完成，最优参数: {result['params']}")
            logger.info(f"最优指标 {args.optimize_metric}: {result['metrics'][args.optimize_metric]}")

            # 使用最优参数重新运行
            for param_name, param_value in result['params'].items():
                setattr(strategy, param_name, param_value)

            portfolio, metrics = engine.run(
                symbol=symbol,
                start_date=args.start,
                end_date=args.end,
                timeframe=args.timeframe,
                apply_risk_management=not args.no_risk_management
            )
        elif args.symbols:
            # 多资产回测
            symbols = args.symbols.split(',')
            logger.info(f"开始多资产回测: {symbols}")

            portfolio, all_metrics = engine.run_multiple(
                symbols=symbols,
                start_date=args.start,
                end_date=args.end,
                timeframe=args.timeframe,
                apply_risk_management=not args.no_risk_management,
                parallel=args.parallel
            )

            # 使用第一个资产的指标作为总体指标
            metrics = all_metrics[symbols[0]]

            # 输出每个资产的关键指标
            for symbol, symbol_metrics in all_metrics.items():
                logger.info(f"{symbol} 总收益率: {symbol_metrics['total_return']:.2%}, 夏普比率: {symbol_metrics['sharpe_ratio']:.2f}")
        else:
            # 单资产回测
            logger.info(f"开始回测: {args.symbol}")

            # 自动添加市场前缀（000/001/002/300开头为深圳，600/601/603开头为上海）
            symbol = args.symbol
            if not symbol.startswith(('sh', 'sz')):
                if symbol.startswith(('000', '001', '002', '300')):
                    symbol = 'sz' + symbol
                elif symbol.startswith(('600', '601', '603')):
                    symbol = 'sh' + symbol
                else:
                    logger.error(f"无法识别的证券代码格式: {symbol}")
                    return None
        
            portfolio, metrics = engine.run(
                symbol=symbol,
                start_date=args.start,
                end_date=args.end,
                timeframe=args.timeframe,
                apply_risk_management=not args.no_risk_management
            )

        # 输出回测结果
        logger.info(f"回测完成，总收益率: {metrics['total_return']:.2%}")
        logger.info(f"年化收益率: {metrics['annual_return']:.2%}")
        logger.info(f"夏普比率: {metrics['sharpe_ratio']:.2f}")
        logger.info(f"最大回撤: {metrics['max_drawdown']:.2%}")

        # 生成报告
        symbol = args.symbols.split(',')[0] if args.symbols else args.symbol
        report_generator = EnhancedReportGenerator(
            portfolio=portfolio,
            metrics=metrics,
            symbol=symbol,
            strategy_name=args.strategy,
            params=strategy_params,
            start_date=args.start,
            end_date=args.end,
            engine=engine  # 传递引擎实例
        )

        # 根据输出格式生成报告
        if args.output_format in ['html', 'both']:
            html_path = report_generator.generate_html_report(args.output_file)
            logger.info(f"HTML报告已生成: {html_path}")

        if args.output_format in ['json', 'both']:
            json_path = report_generator.generate_json_report(args.output_file)
            logger.info(f"JSON报告已生成: {json_path}")

    except Exception as e:
        logger.error(f"回测过程中发生错误: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == '__main__':
    main()
