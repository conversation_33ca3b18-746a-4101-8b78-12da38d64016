import pandas as pd
import numpy as np
import vectorbt as vbt
import logging
import time
from datetime import datetime, timedelta
import concurrent.futures
from typing import List, Dict, Union, Optional, Any, Tuple
from pathlib import Path

from ..data.data_source import DataSource
from ..strategies.base_strategy import BaseStrategy
from ..utils.cache import DataCache
from ..config import ConfigManager
from ..risk import RiskManager, RiskLimitType

class EnhancedBacktestEngine:
    """增强版回测引擎，支持数据缓存、风险管理和并行回测"""

    def __init__(self, data_source: DataSource, strategy: BaseStrategy,
                 initial_capital: Optional[float] = None,
                 commission: Optional[float] = None,
                 slippage: Optional[float] = None,
                 position_size: Optional[float] = None,
                 use_cache: bool = True,
                 cache_expiry: Optional[int] = None):
        """
        初始化回测引擎

        参数:
            data_source (DataSource): 数据源实例
            strategy (BaseStrategy): 策略实例
            initial_capital (float): 初始资金，如果为None则从配置中读取
            commission (float): 手续费率，如果为None则从配置中读取
            slippage (float): 滑点率，如果为None则从配置中读取
            position_size (float): 仓位比例，1.0表示满仓，如果为None则从配置中读取
            use_cache (bool): 是否使用数据缓存
            cache_expiry (int): 缓存过期时间（天），None表示使用默认值
        """
        self.data_source = data_source
        self.strategy = strategy

        # 获取配置
        config = ConfigManager()
        self.initial_capital = initial_capital if initial_capital is not None else config.get("backtest.default_initial_capital", 100000.0)
        self.commission = commission if commission is not None else config.get("backtest.default_commission", 0.0003)
        self.slippage = slippage if slippage is not None else config.get("backtest.default_slippage", 0.001)
        self.position_size = position_size if position_size is not None else config.get("backtest.default_position_size", 1.0)

        # 初始化数据缓存
        self.use_cache = use_cache
        self.cache_expiry = timedelta(days=cache_expiry if cache_expiry is not None else 7)
        if use_cache:
            self.cache = DataCache()

        # 初始化风险管理器
        self.risk_manager = RiskManager()
        self.risk_manager.set_initial_capital(self.initial_capital)

        # 设置默认风险限制
        self._set_default_risk_limits()

        # 获取日志记录器
        self.logger = logging.getLogger('quantengine')

    def _set_default_risk_limits(self):
        """设置默认风险限制"""
        # 从配置中获取风险限制
        config = ConfigManager()

        # 最大回撤限制（默认25%）
        max_drawdown = config.get("risk.max_drawdown", 0.25)
        self.risk_manager.set_risk_limit(RiskLimitType.MAX_DRAWDOWN, max_drawdown)

        # 最大仓位限制（默认100%）
        max_position_size = config.get("risk.max_position_size", 1.0)
        self.risk_manager.set_risk_limit(RiskLimitType.MAX_POSITION_SIZE, max_position_size)

        # 最大集中度限制（默认50%）
        max_concentration = config.get("risk.max_concentration", 0.5)
        self.risk_manager.set_risk_limit(RiskLimitType.MAX_CONCENTRATION, max_concentration)

    def _get_data_with_cache(self, symbol: str, start_date: str, end_date: str, timeframe: str) -> pd.DataFrame:
        """
        获取数据，支持缓存

        参数:
            symbol (str): 证券代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            timeframe (str): 时间周期

        返回:
            pd.DataFrame: 数据
        """
        if not self.use_cache:
            return self.data_source.get_data(symbol, start_date, end_date, timeframe)

        # 构建缓存键
        cache_key = {
            "symbol": symbol,
            "start_date": start_date,
            "end_date": end_date,
            "timeframe": timeframe,
            "source_type": self.data_source.__class__.__name__
        }

        # 尝试从缓存获取数据
        cached_data = self.cache.get(cache_key, self.cache_expiry)
        if cached_data is not None:
            self.logger.info(f"从缓存加载{symbol}数据")
            return cached_data

        # 从数据源获取数据
        self.logger.info(f"从数据源获取{symbol}数据")
        data = self.data_source.get_data(symbol, start_date, end_date, timeframe)

        # 缓存数据
        self.cache.set(cache_key, data)

        return data

    def run(self, symbol: str, start_date: str, end_date: str, timeframe: str = '1d',
            apply_risk_management: bool = True) -> Tuple[vbt.Portfolio, Dict[str, Any]]:
        """
        运行回测

        参数:
            symbol (str): 证券代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            timeframe (str): 时间周期
            apply_risk_management (bool): 是否应用风险管理

        返回:
            Tuple[vbt.Portfolio, Dict[str, Any]]: 回测结果和性能指标
        """
        start_time = time.time()

        # 获取数据
        self.logger.info(f'开始获取{symbol}数据，时间范围:{start_date}至{end_date}')
        data = self._get_data_with_cache(symbol, start_date, end_date, timeframe)
        self.logger.info(f'数据获取完成，共{len(data)}条记录')

        # 运行策略获取信号
        self.logger.info('开始执行策略计算...')
        entries, exits = self.strategy.run(data)
        self.logger.info(f'策略计算完成，生成{entries.sum()}个入场信号和{exits.sum()}个出场信号')

        # 应用风险管理
        if apply_risk_management:
            entries, exits = self._apply_risk_management(data, entries, exits)
            self.logger.info(f'应用风险管理后，剩余{entries.sum()}个入场信号和{exits.sum()}个出场信号')

        # 使用VectorBT进行回测
        self.logger.info('开始执行回测计算...')

        # 确保信号是布尔类型
        entries = entries.astype(bool)
        exits = exits.astype(bool)

        # 检查信号是否有效
        if entries.sum() == 0:
            self.logger.warning('没有入场信号，回测结果可能不正确')
        if exits.sum() == 0:
            self.logger.warning('没有出场信号，回测结果可能不正确')

        # 打印前几个信号的日期，用于调试
        entry_dates = data.index[entries].tolist()[:5]
        exit_dates = data.index[exits].tolist()[:5]
        self.logger.debug(f'前5个入场信号日期: {entry_dates}')
        self.logger.debug(f'前5个出场信号日期: {exit_dates}')

        # 创建投资组合
        portfolio = vbt.Portfolio.from_signals(
            data['close'],
            entries,
            exits,
            init_cash=self.initial_capital,
            fees=self.commission,
            slippage=self.slippage,
            size=self.position_size,
            size_type='percent',
            freq=timeframe
        )

        # 将 portfolio 对象保存为类的属性
        self.portfolio = portfolio

        # 检查交易记录
        trade_count = portfolio.trades.count()
        self.logger.info(f'生成了 {trade_count} 条交易记录')

        # 如果没有交易记录但有信号，尝试使用不同的参数重新计算
        if trade_count == 0 and (entries.sum() > 0 or exits.sum() > 0):
            self.logger.warning('没有生成交易记录，尝试使用不同的参数重新计算...')
            # 尝试使用不同的参数
            portfolio = vbt.Portfolio.from_signals(
                data['close'],
                entries,
                exits,
                init_cash=self.initial_capital,
                fees=self.commission,
                slippage=self.slippage,
                size=self.position_size,
                size_type='percent',
                freq=timeframe,
                accumulate=True,  # 允许累积仓位
                call_seq='auto'   # 自动确定调用顺序
            )
            self.logger.info(f'重新计算后生成了 {portfolio.trades.count()} 条交易记录')
            # 更新 portfolio 对象属性
            self.portfolio = portfolio

        # 计算性能指标
        metrics = self._calculate_performance_metrics(portfolio)

        end_time = time.time()
        execution_time = end_time - start_time
        self.logger.info(f'回测完成，耗时:{execution_time:.2f}秒，最终资产:{portfolio.total_profit():.2f}')

        return portfolio, metrics

    def _apply_risk_management(self, data: pd.DataFrame, entries: pd.Series, exits: pd.Series) -> Tuple[pd.Series, pd.Series]:
        """
        应用风险管理规则

        参数:
            data (pd.DataFrame): 价格数据
            entries (pd.Series): 入场信号
            exits (pd.Series): 出场信号

        返回:
            Tuple[pd.Series, pd.Series]: 调整后的入场和出场信号
        """
        # 计算收益率
        returns = data['close'].pct_change()

        # 应用止损和止盈
        if RiskLimitType.STOP_LOSS in self.risk_manager.risk_limits or RiskLimitType.TAKE_PROFIT in self.risk_manager.risk_limits:
            # 创建新的出场信号
            new_exits = exits.copy()

            # 获取所有入场点
            entry_points = data.index[entries]

            for i, entry_date in enumerate(entry_points):
                # 获取入场价格
                entry_price = data.loc[entry_date, 'close']

                # 获取入场后的数据
                future_data = data.loc[entry_date:].copy()

                # 应用止损
                if RiskLimitType.STOP_LOSS in self.risk_manager.risk_limits:
                    stop_loss_pct = self.risk_manager.risk_limits[RiskLimitType.STOP_LOSS]
                    stop_loss_price = entry_price * (1 - stop_loss_pct)

                    # 检查是否触发止损
                    stop_loss_triggered = future_data['close'] <= stop_loss_price
                    if stop_loss_triggered.any():
                        # 获取第一个触发止损的日期
                        stop_loss_date = future_data.index[stop_loss_triggered.idxmax()]
                        new_exits.loc[stop_loss_date] = True
                        self.logger.info(f"在{stop_loss_date}触发止损，入场价:{entry_price:.2f}，止损价:{future_data.loc[stop_loss_date, 'close']:.2f}")

                # 应用止盈
                if RiskLimitType.TAKE_PROFIT in self.risk_manager.risk_limits:
                    take_profit_pct = self.risk_manager.risk_limits[RiskLimitType.TAKE_PROFIT]
                    take_profit_price = entry_price * (1 + take_profit_pct)

                    # 检查是否触发止盈
                    take_profit_triggered = future_data['close'] >= take_profit_price
                    if take_profit_triggered.any():
                        # 获取第一个触发止盈的日期
                        take_profit_date = future_data.index[take_profit_triggered.idxmax()]
                        new_exits.loc[take_profit_date] = True
                        self.logger.info(f"在{take_profit_date}触发止盈，入场价:{entry_price:.2f}，止盈价:{future_data.loc[take_profit_date, 'close']:.2f}")

            exits = new_exits

        # 应用最大回撤限制
        if RiskLimitType.MAX_DRAWDOWN in self.risk_manager.risk_limits:
            max_drawdown_limit = self.risk_manager.risk_limits[RiskLimitType.MAX_DRAWDOWN]

            # 计算累积收益率
            cumulative_returns = (1 + returns).cumprod()

            # 计算回撤
            rolling_max = cumulative_returns.cummax()
            drawdown = (cumulative_returns / rolling_max - 1)

            # 找出超过最大回撤限制的点
            exceed_drawdown = drawdown < -max_drawdown_limit

            if exceed_drawdown.any():
                # 在超过最大回撤限制的点添加出场信号
                exits = exits | exceed_drawdown
                self.logger.warning(f"检测到{exceed_drawdown.sum()}个点超过最大回撤限制{max_drawdown_limit:.2%}")

        return entries, exits

    def _calculate_performance_metrics(self, portfolio: vbt.Portfolio) -> Dict[str, Any]:
        """
        计算性能指标

        参数:
            portfolio (vbt.Portfolio): 投资组合

        返回:
            Dict[str, Any]: 性能指标
        """
        metrics = {}

        # 基本指标
        metrics['total_return'] = portfolio.total_return()
        metrics['total_profit'] = portfolio.total_profit()
        metrics['sharpe_ratio'] = portfolio.sharpe_ratio()
        metrics['sortino_ratio'] = portfolio.sortino_ratio()
        metrics['max_drawdown'] = portfolio.max_drawdown()
        metrics['win_rate'] = portfolio.trades.win_rate()
        metrics['profit_factor'] = portfolio.trades.profit_factor()

        # 年化收益率
        metrics['annual_return'] = portfolio.annual_returns().mean()  # 使用annual_returns并取平均值

        # 交易统计
        metrics['trade_count'] = portfolio.trades.count()
        metrics['avg_trade_duration'] = portfolio.trades.duration.mean() if portfolio.trades.count() > 0 else 0

        # 波动率
        metrics['volatility'] = portfolio.returns().std() * np.sqrt(252)  # 年化波动率

        # 卡尔玛比率（年化收益率/最大回撤）
        if metrics['max_drawdown'] != 0:
            metrics['calmar_ratio'] = metrics['annual_return'] / abs(metrics['max_drawdown'])
        else:
            metrics['calmar_ratio'] = np.nan

        return metrics

    def run_multiple(self, symbols: List[str], start_date: str, end_date: str, timeframe: str = '1d',
                    apply_risk_management: bool = True, parallel: bool = True) -> Tuple[vbt.Portfolio, Dict[str, Dict[str, Any]]]:
        """
        运行多资产回测

        参数:
            symbols (list): 证券代码列表
            start_date (str): 开始日期
            end_date (str): 结束日期
            timeframe (str): 时间周期
            apply_risk_management (bool): 是否应用风险管理
            parallel (bool): 是否并行处理

        返回:
            Tuple[vbt.Portfolio, Dict[str, Dict[str, Any]]]: 回测结果和每个资产的性能指标
        """
        start_time = time.time()

        # 获取多个资产的数据和信号
        all_data = {}
        all_entries = {}
        all_exits = {}
        all_metrics = {}

        if parallel and len(symbols) > 1:
            # 并行处理
            self.logger.info(f"使用并行处理模式获取{len(symbols)}个资产的数据和信号")

            def process_symbol(symbol):
                # 获取数据
                data = self._get_data_with_cache(symbol, start_date, end_date, timeframe)

                # 运行策略
                entries, exits = self.strategy.run(data)

                # 应用风险管理
                if apply_risk_management:
                    entries, exits = self._apply_risk_management(data, entries, exits)

                return symbol, data, entries, exits

            # 使用线程池并行处理
            with concurrent.futures.ThreadPoolExecutor(max_workers=min(len(symbols), 10)) as executor:
                futures = [executor.submit(process_symbol, symbol) for symbol in symbols]

                for future in concurrent.futures.as_completed(futures):
                    symbol, data, entries, exits = future.result()
                    all_data[symbol] = data['close']
                    all_entries[symbol] = entries
                    all_exits[symbol] = exits
        else:
            # 串行处理
            for symbol in symbols:
                # 获取数据
                data = self._get_data_with_cache(symbol, start_date, end_date, timeframe)
                all_data[symbol] = data['close']

                # 运行策略
                entries, exits = self.strategy.run(data)

                # 应用风险管理
                if apply_risk_management:
                    entries, exits = self._apply_risk_management(data, entries, exits)

                all_entries[symbol] = entries
                all_exits[symbol] = exits

        # 合并数据
        close = pd.DataFrame(all_data)

        # 转换为DataFrame
        entries_df = pd.DataFrame({symbol: all_entries[symbol] for symbol in symbols})
        exits_df = pd.DataFrame({symbol: all_exits[symbol] for symbol in symbols})

        # 确保信号是布尔类型
        entries_df = entries_df.astype(bool)
        exits_df = exits_df.astype(bool)

        # 使用VectorBT进行多资产回测
        self.logger.info('开始执行多资产回测计算...')
        portfolio = vbt.Portfolio.from_signals(
            close,
            entries_df,
            exits_df,
            init_cash=self.initial_capital,
            fees=self.commission,
            slippage=self.slippage,
            size=self.position_size / len(symbols),  # 平均分配资金
            freq=timeframe
        )

        # 计算每个资产的性能指标
        for symbol in symbols:
            # 提取单个资产的投资组合
            symbol_portfolio = portfolio.get_asset_pf(symbol)

            # 计算性能指标
            all_metrics[symbol] = self._calculate_performance_metrics(symbol_portfolio)

        end_time = time.time()
        execution_time = end_time - start_time
        self.logger.info(f'多资产回测完成，耗时:{execution_time:.2f}秒，最终资产:{portfolio.total_profit():.2f}')

        return portfolio, all_metrics

    def optimize_strategy_params(self, symbol: str, start_date: str, end_date: str,
                               param_grid: Dict[str, List[Any]], timeframe: str = '1d',
                               metric: str = 'sharpe_ratio', maximize: bool = True) -> Dict[str, Any]:
        """
        优化策略参数

        参数:
            symbol (str): 证券代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            param_grid (Dict[str, List[Any]]): 参数网格，如{'fast_ma': [5, 10, 15], 'slow_ma': [20, 30, 40]}
            timeframe (str): 时间周期
            metric (str): 优化指标，如'sharpe_ratio', 'total_return', 'max_drawdown'等
            maximize (bool): 是否最大化指标，True表示最大化，False表示最小化

        返回:
            Dict[str, Any]: 最优参数和对应的性能指标
        """
        self.logger.info(f"开始优化策略参数，参数网格: {param_grid}")

        # 获取数据
        data = self._get_data_with_cache(symbol, start_date, end_date, timeframe)

        # 保存原始参数
        original_params = {param: getattr(self.strategy, param) for param in param_grid.keys()}

        # 生成参数组合
        import itertools
        param_names = list(param_grid.keys())
        param_values = list(param_grid.values())
        param_combinations = list(itertools.product(*param_values))

        self.logger.info(f"共有{len(param_combinations)}种参数组合")

        # 存储结果
        results = []

        # 测试每种参数组合
        for i, params in enumerate(param_combinations):
            # 设置参数
            param_dict = dict(zip(param_names, params))
            for param_name, param_value in param_dict.items():
                setattr(self.strategy, param_name, param_value)

            # 运行回测
            self.logger.info(f"测试参数组合 {i+1}/{len(param_combinations)}: {param_dict}")
            portfolio, metrics = self.run(symbol, start_date, end_date, timeframe)

            # 保存结果
            result = {
                'params': param_dict,
                'metrics': metrics
            }
            results.append(result)

        # 恢复原始参数
        for param_name, param_value in original_params.items():
            setattr(self.strategy, param_name, param_value)

        # 根据指定指标排序
        if maximize:
            results.sort(key=lambda x: x['metrics'][metric], reverse=True)
        else:
            results.sort(key=lambda x: x['metrics'][metric])

        # 返回最优结果
        best_result = results[0]
        self.logger.info(f"最优参数: {best_result['params']}, {metric}: {best_result['metrics'][metric]}")

        return best_result

    def get_all_trades(self):
        """
        获取所有交易记录

        返回:
            List[Dict]: 交易记录列表
        """
        trades = []
        # 检查 self.portfolio 是否存在且有交易记录
        if hasattr(self, 'portfolio') and self.portfolio is not None and hasattr(self.portfolio, 'trades') and self.portfolio.trades.count() > 0:
            for i in range(self.portfolio.trades.count()):
                trade = {
                    'entry_time': self.portfolio.trades.entry_idx[i],
                    'exit_time': self.portfolio.trades.exit_idx[i],
                    'entry_price': self.portfolio.trades.entry_price[i],
                    'exit_price': self.portfolio.trades.exit_price[i],
                    'quantity': self.portfolio.trades.size[i],
                    'direction': '多头' if self.portfolio.trades.size[i] > 0 else '空头', # 假设 size > 0 为多头，< 0 为空头
                    'profit_loss': self.portfolio.trades.pnl[i],
                    'duration_days': self.portfolio.trades.duration[i].days
                }
                trades.append(trade)
        return trades
