2025-05-25 16:00:53 - quantengine - INFO - 数据源加载完成
2025-05-25 16:00:53 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-25 16:00:53 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-25 16:00:53 - quantengine - INFO - 正在创建输出目录: output
2025-05-25 16:00:53 - quantengine - INFO - 开始执行回测...
2025-05-25 16:00:53 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-25 16:00:53 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-25 16:00:53 - quantengine - INFO - 开始执行策略计算...
2025-05-25 16:00:55 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-25 16:00:55 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-25 16:00:55 - quantengine - INFO - 开始执行回测计算...
2025-05-25 16:01:02 - quantengine - INFO - 生成了 5 条交易记录
2025-05-25 16:01:02 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-25 16:01:02 - quantengine - INFO - 回测执行完成
2025-05-25 16:01:02 - quantengine - INFO - 开始生成报告...
2025-05-25 16:01:02 - quantengine.report - INFO - 开始生成回测报告...
2025-05-25 16:01:08 - quantengine.report - INFO - 将报告保存到 output
2025-05-25 16:01:08 - quantengine.report - INFO - 报告生成完成
2025-05-25 16:01:08 - quantengine - INFO - 报告生成完成，已保存至 output
2025-05-25 16:14:42 - quantengine - INFO - 数据源加载完成
2025-05-25 16:14:42 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-25 16:14:42 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-25 16:14:42 - quantengine - INFO - 正在创建输出目录: output
2025-05-25 16:14:42 - quantengine - INFO - 开始执行回测...
2025-05-25 16:14:42 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-25 16:14:42 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-25 16:14:42 - quantengine - INFO - 开始执行策略计算...
2025-05-25 16:14:44 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-25 16:14:44 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-25 16:14:44 - quantengine - INFO - 开始执行回测计算...
2025-05-25 16:14:52 - quantengine - INFO - 生成了 5 条交易记录
2025-05-25 16:14:52 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-25 16:14:52 - quantengine - INFO - 回测执行完成
2025-05-25 16:14:52 - quantengine - INFO - 开始生成报告...
2025-05-25 16:14:52 - quantengine.report - INFO - 开始生成回测报告...
2025-05-25 16:14:58 - quantengine.report - INFO - 将报告保存到 output
2025-05-25 16:14:58 - quantengine.report - INFO - 报告生成完成
2025-05-25 16:14:58 - quantengine - INFO - 报告生成完成，已保存至 output
2025-05-25 16:19:45 - quantengine - INFO - 数据源加载完成
2025-05-25 16:19:45 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-25 16:19:45 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-25 16:19:45 - quantengine - INFO - 正在创建输出目录: output
2025-05-25 16:19:45 - quantengine - INFO - 开始执行回测...
2025-05-25 16:19:45 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-25 16:19:45 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-25 16:19:45 - quantengine - INFO - 开始执行策略计算...
2025-05-25 16:19:47 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-25 16:19:47 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-25 16:19:47 - quantengine - INFO - 开始执行回测计算...
2025-05-25 16:19:55 - quantengine - INFO - 生成了 5 条交易记录
2025-05-25 16:19:55 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-25 16:19:55 - quantengine - INFO - 回测执行完成
2025-05-25 16:19:55 - quantengine - INFO - 开始生成报告...
2025-05-25 16:19:55 - quantengine.report - INFO - 开始生成回测报告...
2025-05-25 16:20:01 - quantengine.report - INFO - 将报告保存到 output
2025-05-25 16:20:44 - quantengine - INFO - 数据源加载完成
2025-05-25 16:20:44 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-25 16:20:44 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-25 16:20:44 - quantengine - INFO - 正在创建输出目录: output
2025-05-25 16:20:44 - quantengine - INFO - 开始执行回测...
2025-05-25 16:20:44 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-25 16:20:44 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-25 16:20:44 - quantengine - INFO - 开始执行策略计算...
2025-05-25 16:20:46 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-25 16:20:46 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-25 16:20:46 - quantengine - INFO - 开始执行回测计算...
2025-05-25 16:20:54 - quantengine - INFO - 生成了 5 条交易记录
2025-05-25 16:20:54 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-25 16:20:54 - quantengine - INFO - 回测执行完成
2025-05-25 16:20:54 - quantengine - INFO - 开始生成报告...
2025-05-25 16:20:54 - quantengine.report - INFO - 开始生成回测报告...
2025-05-25 16:20:59 - quantengine.report - INFO - 将报告保存到 output
2025-05-25 16:20:59 - quantengine.report - INFO - 报告生成完成
2025-05-25 16:20:59 - quantengine - INFO - 报告生成完成，已保存至 output
2025-05-25 22:12:49 - quantengine - INFO - 使用数据源: akshare
2025-05-25 22:12:49 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-25 22:12:49 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-25 22:12:49 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-25 22:12:49 - quantengine - INFO - 开始回测: 000001.SZ
2025-05-25 22:12:49 - quantengine - INFO - 开始获取000001.SZ数据，时间范围:2023-01-01至2023-12-31
2025-05-25 22:12:49 - quantengine - INFO - 从数据源获取000001.SZ数据
2025-05-25 22:12:49 - quantengine - ERROR - 回测过程中发生错误: 不支持的证券类型: 000001.SZ
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 284, in main
    portfolio, metrics = engine.run(
                         ^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_engine.py", line 142, in run
    data = self._get_data_with_cache(symbol, start_date, end_date, timeframe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_engine.py", line 116, in _get_data_with_cache
    data = self.data_source.get_data(symbol, start_date, end_date, timeframe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\data\akshare_source.py", line 87, in get_data
    raise ValueError(f"不支持的证券类型: {symbol}")
ValueError: 不支持的证券类型: 000001.SZ
2025-05-25 22:13:32 - quantengine - INFO - 使用数据源: akshare
2025-05-25 22:13:32 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-25 22:13:32 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-25 22:13:32 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-25 22:13:32 - quantengine - INFO - 开始回测: 000001
2025-05-25 22:13:32 - quantengine - INFO - 开始获取000001数据，时间范围:2023-01-01至2023-12-31
2025-05-25 22:13:32 - quantengine - INFO - 从数据源获取000001数据
2025-05-25 22:13:32 - quantengine - ERROR - 回测过程中发生错误: 不支持的证券类型: 000001
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 284, in main
    portfolio, metrics = engine.run(
                         ^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_engine.py", line 142, in run
    data = self._get_data_with_cache(symbol, start_date, end_date, timeframe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_engine.py", line 116, in _get_data_with_cache
    data = self.data_source.get_data(symbol, start_date, end_date, timeframe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\data\akshare_source.py", line 87, in get_data
    raise ValueError(f"不支持的证券类型: {symbol}")
ValueError: 不支持的证券类型: 000001
2025-05-25 22:15:04 - quantengine - INFO - 使用数据源: akshare
2025-05-25 22:15:04 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-25 22:15:04 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-25 22:15:04 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-25 22:15:04 - quantengine - INFO - 开始回测: 000001
2025-05-25 22:15:04 - quantengine - ERROR - 回测过程中发生错误: cannot access local variable 'symbol' where it is not associated with a value
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 285, in main
    if not symbol.startswith(('sh', 'sz')):
           ^^^^^^
UnboundLocalError: cannot access local variable 'symbol' where it is not associated with a value
2025-05-25 22:17:46 - quantengine - INFO - 使用数据源: akshare
2025-05-25 22:17:46 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-25 22:17:46 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-25 22:17:46 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-25 22:17:46 - quantengine - INFO - 开始回测: 600000
2025-05-25 22:17:46 - quantengine - INFO - 开始获取600000数据，时间范围:2023-01-01至2023-12-31
2025-05-25 22:17:46 - quantengine - INFO - 从数据源获取600000数据
2025-05-25 22:17:46 - quantengine - ERROR - 回测过程中发生错误: 不支持的证券类型: 600000
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 295, in main
    portfolio, metrics = engine.run(
                         ^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_engine.py", line 142, in run
    data = self._get_data_with_cache(symbol, start_date, end_date, timeframe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_engine.py", line 116, in _get_data_with_cache
    data = self.data_source.get_data(symbol, start_date, end_date, timeframe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\data\akshare_source.py", line 87, in get_data
    raise ValueError(f"不支持的证券类型: {symbol}")
ValueError: 不支持的证券类型: 600000
2025-05-25 22:20:31 - quantengine - INFO - 使用数据源: akshare
2025-05-25 22:20:31 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-25 22:20:31 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-25 22:20:31 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-25 22:20:31 - quantengine - INFO - 开始回测: 600000
2025-05-25 22:20:31 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-25 22:20:31 - quantengine - INFO - 从数据源获取sh600000数据
2025-05-25 22:20:31 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-25 22:20:31 - quantengine - INFO - 开始执行策略计算...
2025-05-25 22:20:33 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-25 22:20:33 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-25 22:20:33 - quantengine - INFO - 开始执行回测计算...
2025-05-25 22:20:38 - quantengine - INFO - 生成了 7 条交易记录
2025-05-25 22:20:39 - quantengine - ERROR - 回测过程中发生错误: 'Portfolio' object has no attribute 'annual_return'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 295, in main
    portfolio, metrics = engine.run(
                         ^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_engine.py", line 211, in run
    metrics = self._calculate_performance_metrics(portfolio)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_engine.py", line 320, in _calculate_performance_metrics
    metrics['annual_return'] = portfolio.annual_return()
                               ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Portfolio' object has no attribute 'annual_return'. Did you mean: 'annual_returns'?
2025-05-25 22:23:19 - quantengine - INFO - 使用数据源: akshare
2025-05-25 22:23:19 - quantengine.risk - INFO - 设置风险限制: max_drawdown = 0.25
2025-05-25 22:23:19 - quantengine.risk - INFO - 设置风险限制: max_position_size = 1.0
2025-05-25 22:23:19 - quantengine.risk - INFO - 设置风险限制: max_concentration = 0.5
2025-05-25 22:23:19 - quantengine - INFO - 开始回测: 600000
2025-05-25 22:23:19 - quantengine - INFO - 开始获取sh600000数据，时间范围:2023-01-01至2023-12-31
2025-05-25 22:23:19 - quantengine - INFO - 从缓存加载sh600000数据
2025-05-25 22:23:19 - quantengine - INFO - 数据获取完成，共242条记录
2025-05-25 22:23:19 - quantengine - INFO - 开始执行策略计算...
2025-05-25 22:23:20 - quantengine - INFO - 策略计算完成，生成88个入场信号和135个出场信号
2025-05-25 22:23:20 - quantengine - INFO - 应用风险管理后，剩余88个入场信号和135个出场信号
2025-05-25 22:23:20 - quantengine - INFO - 开始执行回测计算...
2025-05-25 22:23:23 - quantengine - INFO - 生成了 7 条交易记录
2025-05-25 22:23:25 - quantengine - INFO - 回测完成，耗时:6.56秒，最终资产:-7730.65
2025-05-25 22:23:25 - quantengine - INFO - 回测完成，总收益率: -7.73%
2025-05-25 22:23:25 - quantengine - INFO - 年化收益率: -7.73%
2025-05-25 22:23:25 - quantengine - INFO - 夏普比率: -0.82
2025-05-25 22:23:25 - quantengine - INFO - 最大回撤: -14.30%
2025-05-25 22:23:26 - quantengine - ERROR - 回测过程中发生错误: 'Portfolio' object has no attribute 'equity'
Traceback (most recent call last):
  File "F:\menqgb\test\quantengine\enhanced_main.py", line 323, in main
    html_path = report_generator.generate_html_report(args.output_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 74, in generate_html_report
    fig = self._create_plotly_dashboard()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 113, in _create_plotly_dashboard
    self._add_equity_curve(fig, row=1, col=1)
  File "F:\menqgb\test\quantengine\backtest\enhanced_report.py", line 164, in _add_equity_curve
    equity = self.portfolio.equity()
             ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Portfolio' object has no attribute 'equity'
