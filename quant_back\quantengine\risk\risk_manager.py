import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import logging
from enum import Enum

class RiskLimitType(Enum):
    """风险限制类型"""
    MAX_DRAWDOWN = "max_drawdown"  # 最大回撤限制
    MAX_LOSS_PER_TRADE = "max_loss_per_trade"  # 单笔最大亏损限制
    MAX_POSITION_SIZE = "max_position_size"  # 最大仓位限制
    MAX_CONCENTRATION = "max_concentration"  # 最大集中度限制
    VOLATILITY_LIMIT = "volatility_limit"  # 波动率限制
    VAR_LIMIT = "var_limit"  # 风险价值(VaR)限制
    STOP_LOSS = "stop_loss"  # 止损限制
    TAKE_PROFIT = "take_profit"  # 止盈限制

class RiskManager:
    """风险管理器"""

    def __init__(self):
        """初始化风险管理器"""
        self.logger = logging.getLogger('quantengine.risk')
        self.risk_limits = {}
        self.position_data = {}
        self.portfolio_value = 0.0
        self.initial_capital = 0.0

    def set_risk_limit(self, limit_type: RiskLimitType, value: float):
        """
        设置风险限制

        参数:
            limit_type (RiskLimitType): 风险限制类型
            value (float): 限制值
        """
        self.risk_limits[limit_type] = value
        self.logger.info(f"设置风险限制: {limit_type.value} = {value}")

    def update_portfolio_state(self, portfolio_value: float, positions: Dict[str, Dict]):
        """
        更新投资组合状态

        参数:
            portfolio_value (float): 投资组合总价值
            positions (Dict[str, Dict]): 当前持仓信息
        """
        self.portfolio_value = portfolio_value
        self.position_data = positions

    def set_initial_capital(self, initial_capital: float):
        """设置初始资金"""
        self.initial_capital = initial_capital

    def check_drawdown_limit(self, current_value: float) -> bool:
        """
        检查回撤限制

        参数:
            current_value (float): 当前投资组合价值

        返回:
            bool: 是否违反风险限制
        """
        if RiskLimitType.MAX_DRAWDOWN not in self.risk_limits:
            return False

        max_drawdown_limit = self.risk_limits[RiskLimitType.MAX_DRAWDOWN]

        # 计算当前回撤
        drawdown = 1 - (current_value / self.initial_capital)

        if drawdown > max_drawdown_limit:
            self.logger.warning(f"违反最大回撤限制: 当前回撤 {drawdown:.2%}, 限制 {max_drawdown_limit:.2%}")
            return True

        return False

    def check_position_size_limit(self, symbol: str, position_size: float) -> bool:
        """
        检查仓位大小限制

        参数:
            symbol (str): 证券代码
            position_size (float): 仓位大小（占总资产比例）

        返回:
            bool: 是否违反风险限制
        """
        if RiskLimitType.MAX_POSITION_SIZE not in self.risk_limits:
            return False

        max_position_size = self.risk_limits[RiskLimitType.MAX_POSITION_SIZE]

        if position_size > max_position_size:
            self.logger.warning(f"违反最大仓位限制: {symbol} 仓位 {position_size:.2%}, 限制 {max_position_size:.2%}")
            return True

        return False

    def check_concentration_limit(self) -> bool:
        """
        检查集中度限制

        返回:
            bool: 是否违反风险限制
        """
        if RiskLimitType.MAX_CONCENTRATION not in self.risk_limits or not self.position_data:
            return False

        max_concentration = self.risk_limits[RiskLimitType.MAX_CONCENTRATION]

        # 计算最大集中度
        position_values = [pos.get('value', 0) for pos in self.position_data.values()]
        if not position_values or self.portfolio_value == 0:
            return False

        max_position_value = max(position_values)
        concentration = max_position_value / self.portfolio_value

        if concentration > max_concentration:
            self.logger.warning(f"违反最大集中度限制: 当前集中度 {concentration:.2%}, 限制 {max_concentration:.2%}")
            return True

        return False

    def calculate_var(self, returns: pd.Series, confidence_level: float = 0.95) -> float:
        """
        计算风险价值(VaR)

        参数:
            returns (pd.Series): 收益率序列
            confidence_level (float): 置信水平

        返回:
            float: VaR值
        """
        # 使用历史模拟法计算VaR
        var = np.percentile(returns, 100 * (1 - confidence_level))
        return -var  # 转换为正值

    def check_var_limit(self, returns: pd.Series) -> bool:
        """
        检查VaR限制

        参数:
            returns (pd.Series): 收益率序列

        返回:
            bool: 是否违反风险限制
        """
        if RiskLimitType.VAR_LIMIT not in self.risk_limits:
            return False

        var_limit = self.risk_limits[RiskLimitType.VAR_LIMIT]

        # 计算当前VaR
        var = self.calculate_var(returns)

        if var > var_limit:
            self.logger.warning(f"违反VaR限制: 当前VaR {var:.2%}, 限制 {var_limit:.2%}")
            return True

        return False

    def apply_stop_loss(self, entry_price: float, current_price: float, is_long: bool = True) -> bool:
        """
        应用止损

        参数:
            entry_price (float): 入场价格
            current_price (float): 当前价格
            is_long (bool): 是否为多头仓位

        返回:
            bool: 是否触发止损
        """
        if RiskLimitType.STOP_LOSS not in self.risk_limits:
            return False

        stop_loss_pct = self.risk_limits[RiskLimitType.STOP_LOSS]

        if is_long:
            loss_pct = (entry_price - current_price) / entry_price
            if loss_pct > stop_loss_pct:
                self.logger.info(f"触发止损: 亏损 {loss_pct:.2%}, 止损线 {stop_loss_pct:.2%}")
                return True
        else:
            loss_pct = (current_price - entry_price) / entry_price
            if loss_pct > stop_loss_pct:
                self.logger.info(f"触发止损: 亏损 {loss_pct:.2%}, 止损线 {stop_loss_pct:.2%}")
                return True

        return False

    def apply_take_profit(self, entry_price: float, current_price: float, is_long: bool = True) -> bool:
        """
        应用止盈

        参数:
            entry_price (float): 入场价格
            current_price (float): 当前价格
            is_long (bool): 是否为多头仓位

        返回:
            bool: 是否触发止盈
        """
        if RiskLimitType.TAKE_PROFIT not in self.risk_limits:
            return False

        take_profit_pct = self.risk_limits[RiskLimitType.TAKE_PROFIT]

        if is_long:
            profit_pct = (current_price - entry_price) / entry_price
            if profit_pct > take_profit_pct:
                self.logger.info(f"触发止盈: 盈利 {profit_pct:.2%}, 止盈线 {take_profit_pct:.2%}")
                return True
        else:
            profit_pct = (entry_price - current_price) / entry_price
            if profit_pct > take_profit_pct:
                self.logger.info(f"触发止盈: 盈利 {profit_pct:.2%}, 止盈线 {take_profit_pct:.2%}")
                return True

        return False

    def check_all_limits(self, portfolio_value: float, positions: Dict[str, Dict],
                         returns: pd.Series) -> List[RiskLimitType]:
        """
        检查所有风险限制

        参数:
            portfolio_value (float): 投资组合总价值
            positions (Dict[str, Dict]): 当前持仓信息
            returns (pd.Series): 收益率序列

        返回:
            List[RiskLimitType]: 违反的风险限制列表
        """
        self.update_portfolio_state(portfolio_value, positions)

        violated_limits = []

        # 检查回撤限制
        if self.check_drawdown_limit(portfolio_value):
            violated_limits.append(RiskLimitType.MAX_DRAWDOWN)

        # 检查集中度限制
        if self.check_concentration_limit():
            violated_limits.append(RiskLimitType.MAX_CONCENTRATION)

        # 检查VaR限制
        if self.check_var_limit(returns):
            violated_limits.append(RiskLimitType.VAR_LIMIT)

        # 检查各个仓位的大小限制
        for symbol, position in positions.items():
            position_size = position.get('value', 0) / portfolio_value
            if self.check_position_size_limit(symbol, position_size):
                violated_limits.append(RiskLimitType.MAX_POSITION_SIZE)
                break

        return violated_limits
