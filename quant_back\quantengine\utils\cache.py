import pandas as pd
from pathlib import Path
import hashlib
import json
import logging
from typing import Optional, Any
from datetime import datetime, timedelta
import pickle
import os

class DataCache:
    """数据缓存系统"""

    def __init__(self, cache_dir: str = None):
        """
        初始化缓存系统

        参数:
            cache_dir (str): 缓存目录路径，默认为项目根目录下的.cache目录
        """
        if cache_dir is None:
            cache_dir = Path(__file__).parent.parent.parent / '.cache'
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger('quantengine.cache')

        # 清理过期缓存
        self._cleanup_expired_cache()

    def _get_cache_key(self, key_components: dict) -> str:
        """
        生成缓存键

        参数:
            key_components (dict): 用于生成缓存键的组件

        返回:
            str: 缓存键
        """
        # 将组件转换为排序后的JSON字符串，确保相同的组件生成相同的键
        key_str = json.dumps(key_components, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()

    def _get_cache_path(self, cache_key: str) -> Path:
        """获取缓存文件路径"""
        return self.cache_dir / f"{cache_key}.pkl"

    def get(self, key_components: dict, max_age: Optional[timedelta] = None) -> Optional[Any]:
        """
        获取缓存数据

        参数:
            key_components (dict): 缓存键组件
            max_age (timedelta): 最大缓存年龄，None表示永不过期

        返回:
            Optional[Any]: 缓存的数据，如果没有找到或已过期则返回None
        """
        cache_key = self._get_cache_key(key_components)
        cache_path = self._get_cache_path(cache_key)

        if not cache_path.exists():
            return None

        # 检查缓存是否过期
        if max_age is not None:
            cache_time = datetime.fromtimestamp(cache_path.stat().st_mtime)
            if datetime.now() - cache_time > max_age:
                self.logger.debug(f"缓存已过期: {cache_key}")
                return None

        try:
            with open(cache_path, 'rb') as f:
                data = pickle.load(f)
                self.logger.debug(f"从缓存加载数据: {cache_key}")
                return data
        except Exception as e:
            self.logger.warning(f"读取缓存失败: {str(e)}")
            return None

    def set(self, key_components: dict, data: Any):
        """
        设置缓存数据

        参数:
            key_components (dict): 缓存键组件
            data (Any): 要缓存的数据
        """
        cache_key = self._get_cache_key(key_components)
        cache_path = self._get_cache_path(cache_key)

        try:
            with open(cache_path, 'wb') as f:
                pickle.dump(data, f)
            self.logger.debug(f"数据已缓存: {cache_key}")
        except Exception as e:
            self.logger.warning(f"缓存数据失败: {str(e)}")

    def _cleanup_expired_cache(self, max_age: timedelta = timedelta(days=30)):
        """
        清理过期缓存文件

        参数:
            max_age (timedelta): 最大缓存年龄，默认30天
        """
        now = datetime.now()
        for cache_file in self.cache_dir.glob("*.pkl"):
            cache_time = datetime.fromtimestamp(cache_file.stat().st_mtime)
            if now - cache_time > max_age:
                try:
                    os.remove(cache_file)
                    self.logger.debug(f"删除过期缓存: {cache_file.name}")
                except Exception as e:
                    self.logger.warning(f"删除过期缓存失败: {str(e)}")

    def clear(self):
        """清空所有缓存"""
        for cache_file in self.cache_dir.glob("*.pkl"):
            try:
                os.remove(cache_file)
            except Exception as e:
                self.logger.warning(f"删除缓存文件失败: {str(e)}")
        self.logger.info("缓存已清空")
