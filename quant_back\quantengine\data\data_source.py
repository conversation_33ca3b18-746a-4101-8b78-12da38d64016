from abc import ABC, abstractmethod
from typing import Optional, Dict, List, Union, Any
import pandas as pd

class DataSource(ABC):
    """数据源基类，定义获取数据的接口"""
    
    @abstractmethod
    def get_data(self, symbol: str, start_date: str, end_date: str, timeframe: str = '1d') -> pd.DataFrame:
        """
        获取历史数据
        
        参数:
            symbol (str): 证券代码
            start_date (str): 开始日期，格式：YYYY-MM-DD
            end_date (str): 结束日期，格式：YYYY-MM-DD
            timeframe (str): 时间周期，支持 '1m', '1h', '1d'
            
        返回:
            pd.DataFrame: 包含OHLCV数据的DataFrame
        """
        pass