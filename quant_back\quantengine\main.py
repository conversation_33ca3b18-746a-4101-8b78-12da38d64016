import argparse
import importlib
import json
import os
import sys
import logging
from pathlib import Path

# 将项目根目录添加到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from quantengine.data.akshare_source import AkshareSource
from quantengine.data.db_source import DBSource
from quantengine.data.mock_source import MockSource
from quantengine.backtest.engine import BacktestEngine
from quantengine.backtest.report import BacktestReport

def main():
    # 初始化日志
    logger = logging.getLogger('quantengine')
    logger.setLevel(logging.DEBUG)  # 改为DEBUG级别以获取更多信息
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    logger.addHandler(handler)

    logger.info('初始化量化回测引擎...')
    parser = argparse.ArgumentParser(description='A股量化回测引擎')
    parser.add_argument('--symbol', type=str, required=True, help='证券代码')
    parser.add_argument('--start', type=str, required=True, help='开始日期 YYYY-MM-DD')
    parser.add_argument('--end', type=str, required=True, help='结束日期 YYYY-MM-DD')
    parser.add_argument('--strategy', type=str, required=True, help='策略名称')
    parser.add_argument('--params', type=str, default='{}', help='策略参数 JSON格式')
    parser.add_argument('--data-source', type=str, default='akshare', help='数据源 (akshare/db/mock)')
    parser.add_argument('--timeframe', type=str, default='1d', help='时间周期 (1m/1h/1d)')
    parser.add_argument('--output', type=str, default='./output', help='输出目录')
    # 移除format参数，因为我们只支持HTML格式

    args = parser.parse_args()
    logger.info(f'参数解析完成: {args}')

    # 加载数据源
    logger.info(f'正在加载数据源: {args.data_source}')
    if args.data_source == 'akshare':
        data_source = AkshareSource()
    elif args.data_source == 'db':
        data_source = DBSource()
    elif args.data_source == 'mock':
        data_source = MockSource()
    else:
        raise ValueError(f"不支持的数据源: {args.data_source}")
    logger.info('数据源加载完成')

    # 动态加载策略
    logger.info(f'正在加载策略: {args.strategy}')
    try:
        # 动态加载策略
        strategy_module_name = args.strategy
        # 策略名称到类名的映射
        strategy_map = {
            'ma_cross': 'MACrossStrategy',
            'rsi_strategy': 'RSIStrategy',
            'dual_ma':'DualMaStrategy',
            # 在这里添加更多策略映射
        }

        if strategy_module_name not in strategy_map:
            raise ValueError(f"不支持的策略名称: {strategy_module_name}，请检查策略名称是否正确")

        strategy_class_name = strategy_map[strategy_module_name]

        strategy_module = importlib.import_module(f"quantengine.strategies.{strategy_module_name}")

        # 获取策略类
        strategy_class = getattr(strategy_module, strategy_class_name)
        strategy_params = json.loads(args.params)
        strategy = strategy_class(params=strategy_params)
        logger.info(f'策略加载成功，类名: {strategy_class_name}，模块: {strategy_module_name}，参数: {strategy_params}')
    except ImportError:
        logger.error(f'策略模块加载失败: quantengine.strategies.{args.strategy}')
        raise ValueError(f"无法加载策略模块 'quantengine.strategies.{args.strategy}'，请检查策略名称是否正确")
    except AttributeError:
        logger.error(f'策略类加载失败: {args.strategy}')
        raise ValueError(f"无法在模块 'quantengine.strategies.{args.strategy}' 中找到策略类 '{args.strategy}'，请检查策略类名是否正确")
    except Exception as e:
        logger.error(f'策略加载失败: {str(e)}')
        raise ValueError(f"加载策略 '{args.strategy}' 时发生未知错误: {str(e)}")

    # 创建输出目录
    logger.info(f'正在创建输出目录: {args.output}')
    os.makedirs(args.output, exist_ok=True)

    # 运行回测
    logger.info('开始执行回测...')
    engine = BacktestEngine(data_source, strategy, initial_capital=100000.0, commission=0.0003, slippage=0.001, position_size=1.0)
    portfolio = engine.run(args.symbol, args.start, args.end, args.timeframe)
    logger.info('回测执行完成')

    # 生成报告
    logger.info('开始生成报告...')
    report = BacktestReport(portfolio, args.symbol, args.strategy)
    report_data = report.generate_report(args.output)

    logger.info(f'报告生成完成，已保存至 {args.output}')
    print(f"回测完成，报告已保存至 {args.output}")

if __name__ == "__main__":
    main()